from docx import Document
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls
from docx.shared import Inches
import xml.etree.ElementTree as ET

class WordExporter:
    def __init__(self, settings):
        self.settings = settings
        
    def create_document(self, equations, filename):
        """Create Word document with proper OMML equations"""
        doc = Document()
        
        # Set document properties
        doc.core_properties.title = "Mathematical Equations"
        doc.core_properties.author = "MathCapture Studio"
        
        # Add title
        title = doc.add_heading('Mathematical Equations', 0)
        title.alignment = 1  # Center alignment
        
        # Process each equation
        for i, equation in enumerate(equations):
            self.add_equation_to_document(doc, equation, i + 1)
            
        doc.save(filename)
        
    def add_equation_to_document(self, doc, equation, number):
        """Add a single equation to the document"""
        # Add equation header
        if self.settings['show_page_refs']:
            header_text = f"Equation {number} (from {equation.filename})"
        else:
            header_text = f"Equation {number}"
            
        header_para = doc.add_paragraph()
        header_run = header_para.add_run(header_text)
        header_run.bold = True
        
        # Add equation
        if self.settings['inline_equations']:
            self.add_inline_equation(doc, equation.latex_text)
        else:
            self.add_block_equation(doc, equation.latex_text)
            
        # Add spacing
        doc.add_paragraph()
        
    def add_block_equation(self, doc, latex_text):
        """Add equation as a centered block"""
        para = doc.add_paragraph()
        para.alignment = 1  # Center alignment
        
        # Convert LaTeX to OMML
        omml_xml = self.latex_to_omml(latex_text)
        
        try:
            # Parse and insert OMML
            math_element = parse_xml(omml_xml)
            para._element.append(math_element)
        except:
            # Fallback to plain text
            para.add_run(latex_text)
            
    def add_inline_equation(self, doc, latex_text):
        """Add equation inline with text"""
        para = doc.add_paragraph()
        para.add_run("Equation: ")
        
        # Convert LaTeX to OMML
        omml_xml = self.latex_to_omml(latex_text)
        
        try:
            # Parse and insert OMML
            math_element = parse_xml(omml_xml)
            para._element.append(math_element)
        except:
            # Fallback to plain text
            para.add_run(latex_text)
            
    def latex_to_omml(self, latex_text):
        """Convert LaTeX to OMML XML"""
        # This is a comprehensive LaTeX to OMML converter
        # In practice, you might use external tools like latexml
        
        # Basic OMML structure
        omml_ns = "http://schemas.openxmlformats.org/officeDocument/2006/math"
        
        # Create root math element
        math_elem = ET.Element(f"{{{omml_ns}}}oMath")
        
        # Parse LaTeX and convert to OMML
        self.parse_latex_to_omml(latex_text, math_elem, omml_ns)
        
        # Convert to string
        return ET.tostring(math_elem, encoding='unicode')
        
    def parse_latex_to_omml(self, latex_text, parent, ns):
        """Parse LaTeX and convert to OMML elements"""
        # This is a simplified parser - a full implementation would
        # need a proper LaTeX parser
        
        # Handle common LaTeX constructs
        if '\\frac' in latex_text:
            self.handle_fraction(latex_text, parent, ns)
        elif '^' in latex_text:
            self.handle_superscript(latex_text, parent, ns)
        elif '_' in latex_text:
            self.handle_subscript(latex_text, parent, ns)
        elif '\\sqrt' in latex_text:
            self.handle_square_root(latex_text, parent, ns)
        else:
            # Simple text run
            r_elem = ET.SubElement(parent, f"{{{ns}}}r")
            t_elem = ET.SubElement(r_elem, f"{{{ns}}}t")
            t_elem.text = latex_text
            
    def handle_fraction(self, latex_text, parent, ns):
        """Handle \frac{numerator}{denominator}"""
        import re
        
        frac_pattern = r'\\frac\{([^}]+)\}\{([^}]+)\}'
        match = re.search(frac_pattern, latex_text)
        
        if match:
            numerator = match.group(1)
            denominator = match.group(2)
            
            # Create fraction element
            f_elem = ET.SubElement(parent, f"{{{ns}}}f")
            
            # Numerator
            num_elem = ET.SubElement(f_elem, f"{{{ns}}}num")
            self.parse_latex_to_omml(numerator, num_elem, ns)
            
            # Denominator
            den_elem = ET.SubElement(f_elem, f"{{{ns}}}den")
            self.parse_latex_to_omml(denominator, den_elem, ns)
            
    def handle_superscript(self, latex_text, parent, ns):
        """Handle superscripts x^{n}"""
        import re
        
        sup_pattern = r'([^\\^]+)\^\{([^}]+)\}'
        match = re.search(sup_pattern, latex_text)
        
        if match:
            base = match.group(1)
            superscript = match.group(2)
            
            # Create superscript element
            ssup_elem = ET.SubElement(parent, f"{{{ns}}}sSup")
            
            # Base
            e_elem = ET.SubElement(ssup_elem, f"{{{ns}}}e")
            self.parse_latex_to_omml(base, e_elem, ns)
            
            # Superscript
            sup_elem = ET.SubElement(ssup_elem, f"{{{ns}}}sup")
            self.parse_latex_to_omml(superscript, sup_elem, ns)
            
    def handle_subscript(self, latex_text, parent, ns):
        """Handle subscripts x_{n}"""
        import re
        
        sub_pattern = r'([^\\^_]+)_\{([^}]+)\}'
        match = re.search(sub_pattern, latex_text)
        
        if match:
            base = match.group(1)
            subscript = match.group(2)
            
            # Create subscript element
            ssub_elem = ET.SubElement(parent, f"{{{ns}}}sSub")
            
            # Base
            e_elem = ET.SubElement(ssub_elem, f"{{{ns}}}e")
            self.parse_latex_to_omml(base, e_elem, ns)
            
            # Subscript
            sub_elem = ET.SubElement(ssub_elem, f"{{{ns}}}sub")
            self.parse_latex_to_omml(subscript, sub_elem, ns)
            
    def handle_square_root(self, latex_text, parent, ns):
        """Handle \sqrt{expression}"""
        import re
        
        sqrt_pattern = r'\\sqrt\{([^}]+)\}'
        match = re.search(sqrt_pattern, latex_text)
        
        if match:
            expression = match.group(1)
            
            # Create radical element
            rad_elem = ET.SubElement(parent, f"{{{ns}}}rad")
            
            # Expression under radical
            e_elem = ET.SubElement(rad_elem, f"{{{ns}}}e")
            self.parse_latex_to_omml(expression, e_elem, ns)