"""
Settings Manager Module for MathCapture Studio - Responsive Edition

Handles application settings with responsive design considerations
and modern configuration management.
"""

import json
import os
import logging
from typing import Dict, Any, Optional

class SettingsManager:
    """Manage application settings with responsive design support"""
    
    def __init__(self, config_dir: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        
        # Determine config directory
        if config_dir is None:
            self.config_dir = os.path.join(
                os.path.expanduser("~"), 
                ".mathcapture_studio"
            )
        else:
            self.config_dir = config_dir
            
        # Ensure config directory exists
        os.makedirs(self.config_dir, exist_ok=True)
        
        self.settings_file = os.path.join(self.config_dir, "settings.json")
        self.default_settings = self._get_default_settings()
        
    def _get_default_settings(self) -> Dict[str, Any]:
        """Get default application settings"""
        return {
            # Application settings
            "version": "2.0.0",
            "first_run": True,
            "auto_save": True,
            "auto_save_interval": 300,  # seconds
            
            # UI settings
            "theme": "modern",
            "font_family": "Segoe UI",
            "font_size": 10,
            "ui_scale": 1.0,
            "remember_window_state": True,
            "window_width": 1400,
            "window_height": 900,
            "window_x": 100,
            "window_y": 100,
            "window_maximized": False,
            
            # Layout settings
            "sidebar_width": 300,
            "editor_width": 350,
            "queue_height": 200,
            "sidebar_visible": True,
            "queue_visible": True,
            "layout_mode": "desktop",
            
            # OCR settings
            "tesseract_path": "",
            "tesseract_psm": 6,
            "ocr_confidence_threshold": 60,
            "auto_latex_conversion": True,
            "ocr_language": "eng",
            "math_detection_enabled": True,
            
            # Image processing settings
            "image_enhancement": "medium",
            "auto_crop": True,
            "skew_correction": True,
            "noise_reduction": True,
            "contrast_enhancement": "clahe",
            
            # Export settings
            "export_dir": os.path.expanduser("~/Documents"),
            "export_format": "docx",
            "inline_equations": False,
            "show_page_refs": True,
            "show_confidence": True,
            "show_metadata": True,
            "word_font_family": "Times New Roman",
            "word_font_size": 12,
            
            # Preview settings
            "live_preview": True,
            "preview_engine": "mathjax",
            "preview_update_delay": 500,  # milliseconds
            
            # File handling settings
            "max_file_size": 100,  # MB
            "supported_formats": ["pdf", "png", "jpg", "jpeg", "bmp", "tiff"],
            "recent_files": [],
            "max_recent_files": 10,
            
            # Performance settings
            "max_zoom": 5.0,
            "min_zoom": 0.1,
            "zoom_step": 1.25,
            "canvas_buffer_size": 2048,
            "thread_pool_size": 4,
            
            # Debug settings
            "debug_mode": False,
            "log_level": "INFO",
            "save_debug_images": False,
            "debug_output_dir": os.path.join(os.path.expanduser("~"), "mathcapture_debug"),

            # AI Settings - Enable AI for better chemistry processing
            "ai_enabled": True,  # Master AI toggle - enable for better results
            "ai_vision_enabled": False,  # Qwen-VL Vision AI for OCR (keep disabled for performance)
            "ai_text_enhancement_enabled": True,  # Qwen2.5-1.5B for LaTeX enhancement
            "ai_subject_processing_enabled": True,  # AI-powered subject-specific processing
            "ai_math_reasoning_enabled": True,  # AI Math Processor reasoning
            "ai_show_status": True,  # Show AI status indicators in UI
            
            # Responsive design settings
            "responsive_breakpoints": {
                "mobile": 1000,
                "tablet": 1300,
                "desktop": 1300
            },
            "adaptive_ui": True,
            "touch_mode": False,
            "high_dpi_scaling": True,
        }
        
    def load_settings(self) -> Dict[str, Any]:
        """Load settings from file or return defaults"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    
                # Merge with defaults to ensure all keys exist
                settings = self.default_settings.copy()
                settings.update(saved_settings)
                
                # Validate settings
                settings = self._validate_settings(settings)
                
                self.logger.info("Settings loaded successfully")
                return settings
            else:
                self.logger.info("No settings file found, using defaults")
                return self.default_settings.copy()
                
        except Exception as e:
            self.logger.error(f"Failed to load settings: {e}")
            return self.default_settings.copy()
            
    def save_settings(self, settings: Dict[str, Any]) -> bool:
        """Save settings to file"""
        try:
            # Validate before saving
            validated_settings = self._validate_settings(settings)
            
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(validated_settings, f, indent=2, ensure_ascii=False)
                
            self.logger.info("Settings saved successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save settings: {e}")
            return False
            
    def _validate_settings(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize settings"""
        validated = settings.copy()
        
        # Validate numeric ranges
        validated["ui_scale"] = max(0.5, min(3.0, validated.get("ui_scale", 1.0)))
        validated["font_size"] = max(8, min(24, validated.get("font_size", 10)))
        validated["word_font_size"] = max(8, min(72, validated.get("word_font_size", 12)))
        validated["ocr_confidence_threshold"] = max(0, min(100, validated.get("ocr_confidence_threshold", 60)))
        validated["max_zoom"] = max(1.0, min(10.0, validated.get("max_zoom", 5.0)))
        validated["min_zoom"] = max(0.05, min(1.0, validated.get("min_zoom", 0.1)))
        
        # Validate paths
        export_dir = validated.get("export_dir", "")
        if not os.path.exists(export_dir):
            validated["export_dir"] = os.path.expanduser("~/Documents")
            
        # Validate window dimensions
        validated["window_width"] = max(800, min(3840, validated.get("window_width", 1400)))
        validated["window_height"] = max(600, min(2160, validated.get("window_height", 900)))
        
        # Validate panel sizes
        validated["sidebar_width"] = max(200, min(600, validated.get("sidebar_width", 300)))
        validated["editor_width"] = max(250, min(800, validated.get("editor_width", 350)))
        validated["queue_height"] = max(100, min(500, validated.get("queue_height", 200)))
        
        return validated
        
    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a specific setting value"""
        settings = self.load_settings()
        return settings.get(key, default)
        
    def set_setting(self, key: str, value: Any) -> bool:
        """Set a specific setting value"""
        settings = self.load_settings()
        settings[key] = value
        return self.save_settings(settings)
        
    def reset_to_defaults(self) -> bool:
        """Reset all settings to defaults"""
        return self.save_settings(self.default_settings.copy())
        
    def export_settings(self, filepath: str) -> bool:
        """Export settings to a file"""
        try:
            settings = self.load_settings()
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            self.logger.error(f"Failed to export settings: {e}")
            return False
            
    def import_settings(self, filepath: str) -> bool:
        """Import settings from a file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            return self.save_settings(imported_settings)
        except Exception as e:
            self.logger.error(f"Failed to import settings: {e}")
            return False
