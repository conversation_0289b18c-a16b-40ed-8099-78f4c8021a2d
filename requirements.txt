# Image processing
Pillow>=9.0.0
opencv-python>=4.5.0
numpy>=1.21.0

# PDF handling
PyMuPDF>=1.20.0
pdf2image>=1.16.0

# OCR
pytesseract>=0.3.10
pix2tex>=0.1.2

# Deep Learning (for LaTeX-OCR and Qwen-VL)
torch>=1.9.0
torchvision>=0.10.0
transformers>=4.26.1

# Qwen2.5-1.5B Text AI (for intelligent LaTeX enhancement - lightweight and efficient)
# Note: Qwen2.5-1.5B is a text model that enhances LaTeX-OCR output
# Removed heavy Qwen-VL dependencies for better performance

# System Performance Monitoring (optional)
psutil>=5.9.0

# Word document creation
python-docx>=0.8.11
lxml>=4.6.0

# Additional utilities
typing-extensions>=4.0.0