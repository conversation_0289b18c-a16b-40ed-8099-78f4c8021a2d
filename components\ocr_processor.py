import cv2
import numpy as np
import pytesseract
from PIL import Image
import re

class MathOCRProcessor:
    def __init__(self, tesseract_config):
        self.config = tesseract_config
        
    def preprocess_image(self, image):
        """Advanced preprocessing for mathematical content"""
        # Convert to OpenCV format
        cv_img = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
        
        # Noise reduction
        denoised = cv2.fastNlMeansDenoising(gray)
        
        # Enhance contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)
        
        # Morphological operations to clean up
        kernel = np.ones((2,2), np.uint8)
        cleaned = cv2.morphologyEx(enhanced, cv2.MORPH_CLOSE, kernel)
        
        # Adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            cleaned, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        return thresh
        
    def extract_text(self, processed_image):
        """Extract text using Tesseract with math-specific settings"""
        try:
            # Try different PSM modes for math content
            psm_modes = [6, 8, 11, 13]
            best_result = ""
            best_confidence = 0
            
            for psm in psm_modes:
                config = f"--psm {psm} {self.config}"
                text = pytesseract.image_to_string(processed_image, config=config)
                
                # Get confidence data
                data = pytesseract.image_to_data(
                    processed_image, config=config, 
                    output_type=pytesseract.Output.DICT
                )
                
                confidences = [int(c) for c in data['conf'] if int(c) > 0]
                avg_conf = sum(confidences) / len(confidences) if confidences else 0
                
                if avg_conf > best_confidence:
                    best_confidence = avg_conf
                    best_result = text
                    
            return best_result.strip(), best_confidence
            
        except Exception as e:
            return "", 0
            
    def convert_to_latex(self, text):
        """Advanced LaTeX conversion with pattern recognition"""
        # Symbol replacements
        symbols = {
            '∫': '\\int', '∑': '\\sum', '∏': '\\prod',
            '√': '\\sqrt', '∞': '\\infty', '∂': '\\partial',
            'α': '\\alpha', 'β': '\\beta', 'γ': '\\gamma',
            'δ': '\\delta', 'ε': '\\epsilon', 'ζ': '\\zeta',
            'η': '\\eta', 'θ': '\\theta', 'ι': '\\iota',
            'κ': '\\kappa', 'λ': '\\lambda', 'μ': '\\mu',
            'ν': '\\nu', 'ξ': '\\xi', 'ο': 'o',
            'π': '\\pi', 'ρ': '\\rho', 'σ': '\\sigma',
            'τ': '\\tau', 'υ': '\\upsilon', 'φ': '\\phi',
            'χ': '\\chi', 'ψ': '\\psi', 'ω': '\\omega',
            '≤': '\\leq', '≥': '\\geq', '≠': '\\neq',
            '±': '\\pm', '∓': '\\mp', '×': '\\times',
            '÷': '\\div', '·': '\\cdot', '∘': '\\circ',
            '∈': '\\in', '∉': '\\notin', '⊂': '\\subset',
            '⊃': '\\supset', '∪': '\\cup', '∩': '\\cap',
            '→': '\\rightarrow', '←': '\\leftarrow',
            '↔': '\\leftrightarrow', '⇒': '\\Rightarrow',
            '⇐': '\\Leftarrow', '⇔': '\\Leftrightarrow'
        }
        
        latex_text = text
        for symbol, latex in symbols.items():
            latex_text = latex_text.replace(symbol, latex)
            
        # Pattern-based conversions
        
        # Fractions: a/b -> \frac{a}{b}
        latex_text = re.sub(r'(\w+|$$[^)]+$$)/(\w+|$$[^)]+$$)', 
                           r'\\frac{\1}{\2}', latex_text)
        
        # Simple fractions: digit/digit
        latex_text = re.sub(r'(\d+)/(\d+)', r'\\frac{\1}{\2}', latex_text)
        
        # Superscripts: x^n -> x^{n}
        latex_text = re.sub(r'\^(\w+)', r'^{\1}', latex_text)
        latex_text = re.sub(r'\^($$[^)]+$$)', r'^{\1}', latex_text)
        
        # Subscripts: x_n -> x_{n}
        latex_text = re.sub(r'_(\w+)', r'_{\1}', latex_text)
        latex_text = re.sub(r'_($$[^)]+$$)', r'_{\1}', latex_text)
        
        # Square roots: sqrt(x) -> \sqrt{x}
        latex_text = re.sub(r'sqrt$$([^)]+)$$', r'\\sqrt{\1}', latex_text)
        
        # Integrals with limits
        latex_text = re.sub(r'∫\s*(\w+)\s*to\s*(\w+)', 
                           r'\\int_{\1}^{\2}', latex_text)
        
        # Summations with limits
        latex_text = re.sub(r'∑\s*(\w+)\s*=\s*(\w+)\s*to\s*(\w+)', 
                           r'\\sum_{\1=\2}^{\3}', latex_text)
        
        return latex_text.strip()