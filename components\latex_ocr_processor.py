"""
LaTeX-OCR Integration for MathCapture Studio
Provides state-of-the-art mathematical image-to-LaTeX conversion using pix2tex
"""

import os
import sys
from PIL import Image
import numpy as np

class LaTeXOCRProcessor:
    """
    Advanced mathematical OCR using LaTeX-OCR (pix2tex) deep learning model
    """
    
    def __init__(self):
        self.model = None
        self.available = False
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize the LaTeX-OCR model with error handling"""
        try:
            from pix2tex.cli import LatexOCR
            print("Initializing LaTeX-OCR model...")
            self.model = LatexOCR()
            self.available = True
            print("✅ LaTeX-OCR model loaded successfully!")
        except ImportError:
            print("⚠️  LaTeX-OCR (pix2tex) not installed. Install with: pip install pix2tex")
            self.available = False
        except Exception as e:
            print(f"❌ Failed to initialize LaTeX-OCR: {e}")
            self.available = False
    
    def is_available(self):
        """Check if LaTeX-OCR is available"""
        return self.available
    
    def process_image(self, image):
        """
        Process image using LaTeX-OCR to get perfect LaTeX output
        
        Args:
            image: PIL Image or numpy array
            
        Returns:
            dict: {
                'latex': str,      # LaTeX code
                'confidence': float, # Confidence score (0-100)
                'method': str      # Processing method used
            }
        """
        if not self.available:
            return {
                'latex': '',
                'confidence': 0.0,
                'method': 'latex_ocr_unavailable'
            }
        
        try:
            # Ensure image is PIL Image
            if isinstance(image, np.ndarray):
                image = Image.fromarray(image)
            elif not isinstance(image, Image.Image):
                raise ValueError("Image must be PIL Image or numpy array")
            
            # Preprocess image for optimal LaTeX-OCR performance
            processed_image = self._preprocess_for_latex_ocr(image)
            
            # Run LaTeX-OCR
            latex_result = self.model(processed_image)
            
            # Calculate confidence based on LaTeX quality
            confidence = self._calculate_confidence(latex_result)
            
            return {
                'latex': latex_result,
                'confidence': confidence,
                'method': 'latex_ocr'
            }
            
        except Exception as e:
            print(f"LaTeX-OCR processing error: {e}")
            return {
                'latex': '',
                'confidence': 0.0,
                'method': 'latex_ocr_error'
            }
    
    def _preprocess_for_latex_ocr(self, image):
        """
        Preprocess image for optimal LaTeX-OCR performance
        
        LaTeX-OCR works best with:
        - Clean, high-contrast images
        - Appropriate resolution (not too large, not too small)
        - White background, black text
        """
        # Convert to RGB if needed
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Get image dimensions
        width, height = image.size
        
        # Resize if image is too large (LaTeX-OCR has optimal size ranges)
        max_dimension = 1024
        if max(width, height) > max_dimension:
            scale = max_dimension / max(width, height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Resize if image is too small
        min_dimension = 64
        if min(width, height) < min_dimension:
            scale = min_dimension / min(width, height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        return image
    
    def _calculate_confidence(self, latex_result):
        """
        Calculate confidence score based on LaTeX output quality
        
        This is a heuristic since LaTeX-OCR doesn't provide confidence scores
        """
        if not latex_result or not latex_result.strip():
            return 0.0
        
        confidence = 50.0  # Base confidence
        
        # Increase confidence for well-formed LaTeX
        quality_indicators = [
            ('\\frac{', 10),      # Proper fractions
            ('\\sqrt{', 8),       # Proper square roots
            ('\\', 5),            # LaTeX commands
            ('{', 3),             # Proper bracing
            ('}', 3),             # Proper bracing
            ('^{', 5),            # Proper superscripts
            ('_{', 5),            # Proper subscripts
        ]
        
        for indicator, points in quality_indicators:
            count = latex_result.count(indicator)
            confidence += count * points
        
        # Penalize for common OCR errors
        error_indicators = [
            ('|', -5),            # Common OCR error
            ('l', -2),            # Often misread
            ('O', -2),            # Often misread as 0
        ]
        
        for indicator, penalty in error_indicators:
            count = latex_result.count(indicator)
            confidence += count * penalty
        
        # Ensure confidence is in valid range
        return max(0.0, min(100.0, confidence))
    
    def batch_process(self, images):
        """
        Process multiple images in batch for efficiency
        
        Args:
            images: List of PIL Images
            
        Returns:
            List of result dictionaries
        """
        if not self.available:
            return [{'latex': '', 'confidence': 0.0, 'method': 'latex_ocr_unavailable'} 
                   for _ in images]
        
        results = []
        for i, image in enumerate(images):
            print(f"Processing image {i+1}/{len(images)} with LaTeX-OCR...")
            result = self.process_image(image)
            results.append(result)
        
        return results
    
    def get_model_info(self):
        """Get information about the LaTeX-OCR model"""
        if not self.available:
            return {
                'available': False,
                'model': 'LaTeX-OCR (pix2tex)',
                'status': 'Not available'
            }
        
        return {
            'available': True,
            'model': 'LaTeX-OCR (pix2tex)',
            'status': 'Ready',
            'description': 'State-of-the-art Vision Transformer for mathematical image-to-LaTeX conversion',
            'advantages': [
                'Specialized for mathematical content',
                'Deep learning based (ViT + Transformer)',
                'High accuracy on complex equations',
                'Handles handwritten and printed math',
                'Direct LaTeX output (no post-processing needed)'
            ]
        }

# Test function
def test_latex_ocr():
    """Test the LaTeX-OCR processor"""
    processor = LaTeXOCRProcessor()
    
    print("LaTeX-OCR Processor Test")
    print("=" * 40)
    
    info = processor.get_model_info()
    print(f"Model: {info['model']}")
    print(f"Status: {info['status']}")
    print(f"Available: {info['available']}")
    
    if info['available']:
        print("\nAdvantages:")
        for advantage in info['advantages']:
            print(f"  • {advantage}")
    
    return processor

if __name__ == "__main__":
    test_latex_ocr()
