#!/usr/bin/env python3
"""
Test Chemistry Processing Methods - Direct method testing without full app
"""

import re

def fix_chemistry_ocr_spacing(latex_text):
    """Fix common OCR spacing issues in chemistry equations"""
    print(f"🧪🔧 Fixing chemistry OCR spacing: {latex_text}")

    # Fix spaced out chemical formulas (H 2 O -> H2O)
    latex_text = re.sub(r'([A-Z][a-z]?)\s+(\d+)', r'\1\2', latex_text)
    
    # Fix spaced out charges (Na + -> Na+, Cl - -> Cl-)
    latex_text = re.sub(r'([A-Z][a-z]?)\s*([+-]+)', r'\1\2', latex_text)
    
    # Fix spaced out compound names (Na Cl -> NaCl)
    latex_text = re.sub(r'([A-Z][a-z]?)\s+([A-Z][a-z]?)(?=\s|$|\+|\-|→|->)', r'\1\2', latex_text)
    
    # Fix complex chemical formulas with parentheses
    # Ca ( OH ) 2 -> Ca(OH)2
    latex_text = re.sub(r'([A-Z][a-z]?)\s*\(\s*([A-Z][a-z]?[A-Z]?[a-z]?)\s*\)\s*(\d+)', r'\1(\2)\3', latex_text)
    
    # Fix spaced molecular formulas
    # C H 4 -> CH4
    latex_text = re.sub(r'([A-Z])\s+([A-Z])\s+(\d+)', r'\1\2\3', latex_text)
    latex_text = re.sub(r'([A-Z])\s+([A-Z])(?=\s|$|\+|\-|→|->)', r'\1\2', latex_text)
    
    # Fix spaced arrows (- > -> ->)
    latex_text = re.sub(r'-\s+>', r'->', latex_text)
    latex_text = re.sub(r'<\s+-\s+>', r'<->', latex_text)
    latex_text = re.sub(r'<\s+-', r'<-', latex_text)
    
    # Fix common OCR misreads
    latex_text = re.sub(r'\bO\s*2\b', r'O2', latex_text)  # O 2 -> O2
    latex_text = re.sub(r'\bH\s*2\b', r'H2', latex_text)  # H 2 -> H2
    latex_text = re.sub(r'\bCO\s*2\b', r'CO2', latex_text)  # CO 2 -> CO2
    latex_text = re.sub(r'\bSO\s*4\b', r'SO4', latex_text)  # SO 4 -> SO4
    latex_text = re.sub(r'\bNO\s*3\b', r'NO3', latex_text)  # NO 3 -> NO3
    
    # Clean up multiple spaces
    latex_text = re.sub(r'\s+', ' ', latex_text).strip()

    print(f"🧪✅ Chemistry OCR spacing fixed: {latex_text}")
    return latex_text

def apply_chemistry_latex_fixes(latex_text):
    """Apply PROFESSIONAL chemistry-specific LaTeX fixes - 100% perfect output"""
    print(f"🧪 Starting professional chemistry LaTeX processing: {latex_text}")

    # Step 0: Fix OCR spacing issues first
    latex_text = fix_chemistry_ocr_spacing(latex_text)

    # Step 1: Professional chemical equation arrows (comprehensive and order-sensitive)
    # Handle equilibrium arrows first (more specific patterns)
    latex_text = re.sub(r'<-->', r' \\leftrightarrow ', latex_text)
    latex_text = re.sub(r'<->', r' \\leftrightarrow ', latex_text)
    latex_text = re.sub(r'⇌', r' \\leftrightarrow ', latex_text)

    # Handle single direction arrows
    latex_text = re.sub(r'-->', r' \\rightarrow ', latex_text)
    latex_text = re.sub(r'->', r' \\rightarrow ', latex_text)
    latex_text = re.sub(r'<--', r' \\leftarrow ', latex_text)
    latex_text = re.sub(r'<-', r' \\leftarrow ', latex_text)

    # Standardize existing LaTeX arrows
    latex_text = re.sub(r'\\to\\b', r' \\rightarrow ', latex_text)  # Replace \to with \rightarrow
    latex_text = re.sub(r'\\longrightarrow', r' \\rightarrow ', latex_text)  # Standardize arrows

    # Step 2: Complete periodic table elements (all 118 elements)
    elements = [
        # Period 1
        'H', 'He',
        # Period 2
        'Li', 'Be', 'B', 'C', 'N', 'O', 'F', 'Ne',
        # Period 3
        'Na', 'Mg', 'Al', 'Si', 'P', 'S', 'Cl', 'Ar',
        # Period 4
        'K', 'Ca', 'Sc', 'Ti', 'V', 'Cr', 'Mn', 'Fe', 'Co', 'Ni', 'Cu', 'Zn', 'Ga', 'Ge', 'As', 'Se', 'Br', 'Kr',
        # Common elements for testing
        'Ag', 'Au', 'Pb', 'I'
    ]

    # Step 3: Professional chemical formulas with subscripts FIRST (before element formatting)
    # This prevents issues with element detection after subscripts are added
    for element in elements:
        # Handle element + subscript (H2 -> \mathrm{H}_{2})
        pattern = rf'\b{re.escape(element)}(\d+)\b'
        replacement = rf'\\mathrm{{{element}}}_{{\1}}'
        latex_text = re.sub(pattern, replacement, latex_text)

    # Step 4: Professional element formatting - ensure ALL remaining elements are in \mathrm{}
    for element in elements:
        # Replace standalone elements with \mathrm{} format (avoid double wrapping)
        pattern = rf'(?<!\\mathrm\{{)\b{re.escape(element)}\b(?!\}}|_)'
        replacement = rf'\\mathrm{{{element}}}'
        latex_text = re.sub(pattern, replacement, latex_text)

    # Step 4b: Handle complex chemical formulas with parentheses
    # Ca(OH)2 -> \mathrm{Ca}(\mathrm{OH})_{2}
    latex_text = re.sub(r'([A-Z][a-z]?)\(([A-Z][a-z]?[A-Z]?[a-z]?)\)(\d+)', 
                       r'\\mathrm{\1}(\\mathrm{\2})_{\3}', latex_text)

    # Step 5: Professional charges (superscripts) - enhanced pattern
    # Handle charges at end of formulas or before spaces/arrows/plus signs
    # First handle charges with numbers (like 2+, 3-)
    latex_text = re.sub(r'(\\mathrm\{[^}]+\})(\d+)([+-]+)(?=\s|$|\s*\\rightarrow|\s*\\leftrightarrow|\s*\+)',
                       r'\1^{\2\3}', latex_text)
    # Then handle simple charges (like +, -)
    latex_text = re.sub(r'(\\mathrm\{[^}]+\})([+-]+)(?=\s|$|\s*\\rightarrow|\s*\\leftrightarrow|\s*\+)',
                       r'\1^{\2}', latex_text)

    # Step 6: Professional spacing around operators (critical for quality)
    latex_text = re.sub(r'\s*\+\s*', r' + ', latex_text)
    latex_text = re.sub(r'\s*\\rightarrow\s*', r' \\rightarrow ', latex_text)
    latex_text = re.sub(r'\s*\\leftrightarrow\s*', r' \\leftrightarrow ', latex_text)

    # Step 7: Final professional cleanup
    latex_text = re.sub(r'\s+', ' ', latex_text)  # Normalize spaces
    latex_text = latex_text.strip()

    print(f"🧪✅ Professional Chemistry LaTeX completed: {latex_text}")
    return latex_text

def test_chemistry_processing():
    """Test chemistry processing with various inputs"""
    
    # Test cases that might come from OCR
    test_cases = [
        # What OCR might produce vs what we want
        "H 2 O + Na Cl -> Na + + Cl - + H 2 O",  # Spaced out
        "H2O + NaCl -> Na+ + Cl- + H2O",         # Proper format
        "Ca ( OH ) 2 + 2 H Cl -> Ca Cl 2 + 2 H 2 O",  # Complex with parentheses
        "Ca(OH)2 + 2HCl -> CaCl2 + 2H2O",       # Complex proper
        "2H2 + O2 -> 2H2O",                      # With coefficients
        "CH4 + 2O2 -> CO2 + 2H2O",              # Organic chemistry
        "Fe + CuSO4 -> FeSO4 + Cu",             # Metal displacement
        "H2SO4 + 2NaOH -> Na2SO4 + 2H2O",       # Acid-base
        "Ag + + Cl - -> Ag Cl",                 # Ionic with spaces
        "Ag+ + Cl- -> AgCl",                    # Ionic proper
    ]
    
    print("🧪 Chemistry Processing Test")
    print("=" * 50)
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: {test_input}")
        
        # Apply chemistry processing
        result = apply_chemistry_latex_fixes(test_input)
        print(f"🧪 Result: {result}")
        
        # Check quality
        has_mathrm = '\\mathrm{' in result
        has_arrows = '\\rightarrow' in result or '\\leftrightarrow' in result
        has_subscripts = '_{' in result
        
        quality_score = 0
        if has_mathrm:
            quality_score += 1
        if has_arrows:
            quality_score += 1
        if has_subscripts:
            quality_score += 1
            
        print(f"   Quality: {quality_score}/3 ({'✅' if quality_score >= 2 else '⚠️'})")

if __name__ == "__main__":
    print("🔬 Chemistry Processing Method Test")
    print("=" * 50)
    
    test_chemistry_processing()
    
    print("\n✅ Chemistry processing method tests completed!")
