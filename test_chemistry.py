#!/usr/bin/env python3
"""
Test Chemistry Processing - Debug why chemical symbols are not being recognized
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_qwen25_chemistry():
    """Test Qwen2.5 chemistry processing"""
    print("🧪 Testing Qwen2.5 Chemistry Processing...")
    
    try:
        from components.qwen25_text_processor import Qwen25TextProcessor
        
        processor = Qwen25TextProcessor()
        
        if processor.is_available():
            print("✅ Qwen2.5 is available!")
            
            # Test chemistry input
            test_input = "H2O + NaCl -> Na+ + Cl- + H2O"
            result = processor.get_chemistry_latex_only(test_input)
            
            print(f"📝 Input: {test_input}")
            print(f"🧪 Output: {result}")
            
        else:
            print("❌ Qwen2.5 is not available")
            
    except Exception as e:
        print(f"❌ Qwen2.5 test failed: {e}")

def test_ai_math_processor_chemistry():
    """Test AI Math Processor chemistry processing"""
    print("\n🧪 Testing AI Math Processor Chemistry...")
    
    try:
        from ai_math_processor import AIMathProcessor
        
        processor = AIMathProcessor()
        
        # Test chemistry input
        test_input = "H2O + NaCl -> Na+ + Cl- + H2O"
        result = processor.get_chemistry_latex_only(test_input)
        
        print(f"📝 Input: {test_input}")
        print(f"🧪 Output: {result}")
        
    except Exception as e:
        print(f"❌ AI Math Processor test failed: {e}")

def test_rule_based_chemistry():
    """Test rule-based chemistry processing"""
    print("\n🧪 Testing Rule-Based Chemistry Processing...")
    
    try:
        # Import main app to test rule-based processing
        from main import MathCaptureApp
        
        # Create a minimal app instance
        app = MathCaptureApp()
        
        # Test chemistry input
        test_input = "H2O + NaCl -> Na+ + Cl- + H2O"
        result = app.apply_chemistry_latex_fixes(test_input)
        
        print(f"📝 Input: {test_input}")
        print(f"🧪 Output: {result}")
        
    except Exception as e:
        print(f"❌ Rule-based test failed: {e}")

def test_chemistry_patterns():
    """Test individual chemistry pattern recognition"""
    print("\n🧪 Testing Chemistry Pattern Recognition...")
    
    import re
    
    test_cases = [
        "H2O",
        "NaCl", 
        "CO2",
        "H2SO4",
        "Ca(OH)2",
        "H2O + NaCl",
        "H2O -> H+ + OH-",
        "2H2 + O2 -> 2H2O"
    ]
    
    # Chemical element pattern
    element_pattern = r'\b([A-Z][a-z]?)(\d+)?\b'
    
    for test in test_cases:
        print(f"\n📝 Testing: {test}")
        
        # Find chemical elements
        elements = re.findall(element_pattern, test)
        print(f"   Elements found: {elements}")
        
        # Apply basic formatting
        formatted = re.sub(r'\b([A-Z][a-z]?)(\d+)\b', r'\\mathrm{\1}_{\2}', test)
        formatted = re.sub(r'-->', r' \\rightarrow ', formatted)
        formatted = re.sub(r'->', r' \\rightarrow ', formatted)
        
        print(f"   Formatted: {formatted}")

if __name__ == "__main__":
    print("🔬 Chemistry Processing Debug Test")
    print("=" * 50)
    
    # Test all chemistry processing methods
    test_qwen25_chemistry()
    test_ai_math_processor_chemistry()
    test_rule_based_chemistry()
    test_chemistry_patterns()
    
    print("\n✅ Chemistry processing tests completed!")
