#!/usr/bin/env python3
"""
Test script for Qwen-VL Vision AI integration in MathCapture Studio
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
import numpy as np

# Add the components directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'components'))

def create_test_math_image():
    """Create a simple test image with mathematical equation"""
    # Create a white image
    img = Image.new('RGB', (400, 100), color='white')
    draw = ImageDraw.Draw(img)
    
    # Try to use a font, fallback to default if not available
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    # Draw a simple mathematical equation
    equation = "x² + 2x + 1 = 0"
    draw.text((50, 30), equation, fill='black', font=font)
    
    return img

def test_qwen_vl_processor():
    """Test the Qwen-VL processor"""
    print("🧪 Testing Qwen-VL Vision AI Processor...")
    print("=" * 50)
    
    try:
        # Import the processor
        from qwen_vl_processor import QwenVLProcessor
        
        # Initialize the processor
        print("1. Initializing Qwen-VL processor...")
        processor = QwenVLProcessor()
        
        # Check if it's available
        print(f"2. Processor available: {processor.is_available()}")
        
        if processor.is_available():
            print("3. Model info:")
            model_info = processor.get_model_info()
            for key, value in model_info.items():
                print(f"   {key}: {value}")
            
            # Create test image
            print("4. Creating test mathematical image...")
            test_image = create_test_math_image()
            
            # Test with PIL Image
            print("5. Testing with PIL Image...")
            result = processor.process_image(test_image, "Mathematics")
            
            if result:
                print(f"   ✅ Success! LaTeX: {result['latex']}")
                print(f"   Confidence: {result['confidence']}%")
            else:
                print("   ❌ Failed to process image")
            
            # Test with different subjects
            subjects = ["Mathematics", "Chemistry", "Physics"]
            for subject in subjects:
                print(f"6. Testing {subject} processing...")
                result = processor.process_image(test_image, subject)
                if result:
                    print(f"   ✅ {subject}: {result['latex']}")
                else:
                    print(f"   ❌ {subject}: Failed")
        
        else:
            print("❌ Qwen-VL processor is not available")
            print("💡 This could be due to:")
            print("   - Missing dependencies (transformers, torch, qwen-vl-utils)")
            print("   - Model download issues")
            print("   - Insufficient memory")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install transformers torch torchvision qwen-vl-utils")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

def test_fallback_behavior():
    """Test fallback behavior when Qwen-VL is not available"""
    print("\n🧪 Testing fallback behavior...")
    print("=" * 50)
    
    try:
        # Test the main application's OCR processing
        sys.path.append('.')
        from main import MathCaptureStudio
        
        print("1. Initializing MathCapture Studio...")
        app = MathCaptureStudio()
        
        # Check OCR methods available
        print("2. Checking available OCR methods...")
        if hasattr(app, 'qwen_vl') and app.qwen_vl and app.qwen_vl.is_available():
            print("   ✅ Qwen-VL available")
        else:
            print("   ⚠️ Qwen-VL not available")
            
        if hasattr(app, 'latex_ocr') and app.latex_ocr and app.latex_ocr.is_available():
            print("   ✅ LaTeX-OCR available as fallback")
        else:
            print("   ❌ LaTeX-OCR not available")
        
        # Don't start the GUI
        print("3. Application initialized successfully")
        
    except Exception as e:
        print(f"❌ Error testing main application: {e}")

if __name__ == "__main__":
    print("🚀 Qwen-VL Integration Test Suite")
    print("=" * 60)
    
    # Test 1: Qwen-VL processor directly
    test_qwen_vl_processor()
    
    # Test 2: Fallback behavior
    test_fallback_behavior()
    
    print("\n✅ Test suite completed!")
    print("💡 If Qwen-VL is not working, the application will automatically")
    print("   fall back to LaTeX-OCR for mathematical image processing.")
