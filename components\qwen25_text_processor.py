#!/usr/bin/env python3
"""
Qwen2.5-1.5B Text Processor for MathCapture Studio
Intelligent post-processing of LaTeX-OCR output using Qwen2.5-1.5B
"""

import os
import sys
import time
from typing import Dict, Optional

class Qwen25TextProcessor:
    """Qwen2.5-1.5B processor for intelligent LaTeX post-processing"""
    
    def __init__(self):
        """Initialize Qwen2.5-1.5B text processor"""
        self.model_name = "Qwen/Qwen2.5-1.5B"
        self.available = False
        self.model_loaded = False
        self.model = None
        self.tokenizer = None
        self.device = None
        
        print("🧠 Initializing Qwen2.5-1.5B for intelligent LaTeX processing...")
        
        try:
            self._check_dependencies()
            self._initialize_model()
            
            if self.model_loaded:
                self.available = True
                print("✅ Qwen2.5-1.5B ready for intelligent LaTeX enhancement!")
            else:
                print("⚠️ Qwen2.5-1.5B not loaded - will use basic processing")
                
        except Exception as e:
            print(f"⚠️ Qwen2.5-1.5B initialization failed: {e}")
            print("🔄 Will use basic LaTeX processing")
            self.available = False

    def _check_dependencies(self):
        """Check if required dependencies are available"""
        try:
            # Check if transformers is available
            import transformers
            print("✅ Transformers library available")
            
            # Check version
            version = transformers.__version__
            if version >= "4.37.0":
                print(f"✅ Transformers version {version} (compatible)")
            else:
                print(f"⚠️ Transformers version {version} - recommend 4.37.0+")
            
            # Check if torch is available
            import torch
            print("✅ PyTorch available")
            
            # Check if we have GPU support
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                print(f"✅ CUDA available - GPU: {gpu_name}")
                print(f"✅ GPU Memory: {gpu_memory:.1f} GB")
            else:
                print("⚠️ CUDA not available - will use CPU")
                
            return True
            
        except ImportError as e:
            print(f"❌ Missing dependency: {e}")
            return False

    def _initialize_model(self):
        """Initialize the Qwen2.5-1.5B model"""
        try:
            print("🔄 Loading Qwen2.5-1.5B text model...")
            print("⚠️ Note: First-time model download may take 2-3 minutes...")
            
            from transformers import AutoModelForCausalLM, AutoTokenizer
            import torch
            
            print(f"📥 Loading model from: {self.model_name}")
            
            # Load tokenizer
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_name,
                    trust_remote_code=True
                )
                print("✅ Tokenizer loaded successfully")
            except Exception as e:
                print(f"❌ Failed to load tokenizer: {e}")
                raise
            
            # Load model with memory optimization
            device = "cuda" if torch.cuda.is_available() else "cpu"
            print(f"🎯 Loading model on {device}...")
            
            try:
                if device == "cuda":
                    # Use GPU with memory optimization
                    self.model = AutoModelForCausalLM.from_pretrained(
                        self.model_name,
                        torch_dtype=torch.float16,  # Use float16 for memory efficiency
                        device_map="auto",
                        trust_remote_code=True,
                        low_cpu_mem_usage=True
                    ).eval()
                    print("🚀 GPU acceleration enabled for text processing!")
                else:
                    # Use CPU with memory optimization
                    print("⚠️ Using CPU - processing will be slower but functional")
                    self.model = AutoModelForCausalLM.from_pretrained(
                        self.model_name,
                        torch_dtype=torch.float32,
                        device_map="cpu",
                        trust_remote_code=True,
                        low_cpu_mem_usage=True
                    ).eval()
                
                print("✅ Model loaded successfully")
            except Exception as e:
                print(f"❌ Failed to load model: {e}")
                raise
            
            self.device = device
            self.model_loaded = True
            
            print(f"✅ Qwen2.5-1.5B loaded successfully on {device}")
            print("🚀 Ready for intelligent LaTeX enhancement!")
            
        except Exception as e:
            print(f"❌ Failed to load Qwen2.5-1.5B model: {e}")
            print("💡 Common solutions:")
            print("   1. Ensure transformers>=4.37.0: pip install --upgrade transformers")
            print("   2. Ensure stable internet connection for model download")
            print("   3. Check available disk space (model is ~1.5GB)")
            print("   4. Ensure sufficient RAM (model needs ~3GB)")
            print("🔄 Application will use basic LaTeX processing")
            self.model_loaded = False

    def is_available(self):
        """Check if Qwen2.5-1.5B is available and ready"""
        return self.available and self.model_loaded

    def enhance_latex(self, latex_text: str, subject: str = "Mathematics") -> Dict[str, any]:
        """
        Enhance LaTeX output using Qwen2.5-1.5B intelligence
        
        Args:
            latex_text: Raw LaTeX text from OCR
            subject: Subject context (Mathematics, Chemistry, Physics)
            
        Returns:
            dict: {'latex': str, 'confidence': float, 'improvements': list}
        """
        if not self.is_available():
            # Return original text if model not available
            return {
                'latex': latex_text,
                'confidence': 75.0,
                'improvements': ['Model not available - using original']
            }
            
        try:
            print(f"🧠 Enhancing LaTeX with Qwen2.5-1.5B for {subject}...")
            
            # Create enhancement prompt
            prompt = self._create_enhancement_prompt(latex_text, subject)
            
            # Process with Qwen2.5-1.5B
            enhanced_latex = self._process_with_qwen25(prompt)

            if enhanced_latex and enhanced_latex != latex_text and self._is_valid_latex(enhanced_latex):
                # Calculate improvements
                improvements = self._analyze_improvements(latex_text, enhanced_latex)
                confidence = 90.0  # High confidence for AI enhancement

                result = {
                    'latex': enhanced_latex,
                    'confidence': confidence,
                    'improvements': improvements
                }

                print(f"🧠✅ LaTeX enhanced successfully")
                return result
            else:
                # No improvements made or invalid output
                return {
                    'latex': latex_text,
                    'confidence': 80.0,
                    'improvements': ['No enhancements needed or invalid output']
                }
                
        except Exception as e:
            print(f"❌ LaTeX enhancement error: {e}")
            # Return original on error
            return {
                'latex': latex_text,
                'confidence': 75.0,
                'improvements': [f'Enhancement failed: {str(e)}']
            }

    def _create_enhancement_prompt(self, latex_text: str, subject: str) -> str:
        """Create enhancement prompt for Qwen2.5-1.5B"""

        # Create a more focused prompt that asks for ONLY the LaTeX code
        base_prompt = f"""Fix this LaTeX code and return ONLY the corrected LaTeX:

{latex_text}

Return only the corrected LaTeX code, nothing else."""

        return base_prompt

    def _process_with_qwen25(self, prompt: str) -> Optional[str]:
        """Process prompt with Qwen2.5-1.5B model"""
        try:
            import torch

            # Tokenize input
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=1024)
            inputs = inputs.to(self.device)

            # Generate response with more conservative settings
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=256,  # Reduced to prevent rambling
                    do_sample=False,     # Use greedy decoding for consistency
                    temperature=0.1,     # Very low temperature
                    pad_token_id=self.tokenizer.eos_token_id
                )

            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

            # Extract the response after the prompt
            if prompt in response:
                enhanced_latex = response.replace(prompt, "").strip()
            else:
                enhanced_latex = response.strip()

            return self._clean_latex_output(enhanced_latex)

        except Exception as e:
            print(f"❌ Qwen2.5 processing error: {e}")
            return None

    def _clean_latex_output(self, latex_text: str) -> str:
        """Clean and validate LaTeX output"""
        # Remove any explanatory text and keep only LaTeX
        lines = latex_text.split('\n')
        latex_lines = []

        for line in lines:
            line = line.strip()
            # Skip empty lines and explanatory text
            if not line:
                continue
            if line.startswith(('Note:', 'Explanation:', 'Here', 'The', 'This', 'I', 'Certainly')):
                continue
            if line.startswith(('Fix', 'Return', 'Correct', 'Enhanced')):
                continue
            # Keep lines that look like LaTeX
            if '\\' in line or any(char in line for char in '{}^_=+-*/()[]'):
                latex_lines.append(line)

        result = ' '.join(latex_lines).strip()

        # If result is empty or too long, return original
        if not result or len(result) > 500:
            return latex_text.strip()

        return result

    def _is_valid_latex(self, latex_text: str) -> bool:
        """Check if the LaTeX output is valid and reasonable"""
        # Basic validation checks
        if not latex_text or len(latex_text) < 3:
            return False

        # Check for reasonable length (not too long)
        if len(latex_text) > 1000:
            return False

        # Check for obvious garbage patterns
        garbage_patterns = [
            'Focus on:', 'Please provide', 'Here\'s', 'Certainly!',
            'detailed explanation', 'improve the code', 'enhanced version'
        ]

        for pattern in garbage_patterns:
            if pattern in latex_text:
                return False

        # Should contain some mathematical content
        if not any(char in latex_text for char in '\\{}^_=+-*/()[]'):
            return False

        return True

    def _analyze_improvements(self, original: str, enhanced: str) -> list:
        """Analyze what improvements were made"""
        improvements = []
        
        # Check for common improvements
        if len(enhanced) > len(original):
            improvements.append("Added missing formatting")
        
        if '\\mathrm{' in enhanced and '\\mathrm{' not in original:
            improvements.append("Added proper chemical element formatting")
        
        if '\\frac{' in enhanced and '\\frac{' not in original:
            improvements.append("Improved fraction formatting")
        
        if enhanced.count('\\') > original.count('\\'):
            improvements.append("Enhanced LaTeX command usage")
        
        if not improvements:
            improvements.append("General formatting improvements")
        
        return improvements

    def get_chemistry_latex_only(self, latex_text: str) -> str:
        """
        Get perfect chemistry LaTeX output only - no reasoning, no analysis

        Args:
            latex_text: Raw LaTeX text from OCR

        Returns:
            str: Perfect chemistry LaTeX code only
        """
        if not self.is_available():
            # Return enhanced text using rule-based chemistry processing
            return self._apply_chemistry_latex_rules(latex_text)

        try:
            print(f"🧪🤖 Generating perfect chemistry LaTeX...")

            # Create chemistry LaTeX-only prompt
            prompt = self._create_chemistry_latex_prompt(latex_text)

            # Process with model
            import torch
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=1024)
            inputs = inputs.to(self.device)

            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=256,
                    temperature=0.1,  # Low temperature for consistent output
                    do_sample=False,  # Greedy decoding for consistency
                    pad_token_id=self.tokenizer.eos_token_id
                )

            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

            # Extract LaTeX from response
            if prompt in response:
                latex_output = response.replace(prompt, "").strip()
            else:
                latex_output = response.strip()

            # Clean and validate chemistry LaTeX
            final_latex = self._clean_chemistry_latex(latex_output)

            print(f"🧪✅ Perfect chemistry LaTeX: {final_latex}")
            return final_latex

        except Exception as e:
            print(f"🧪❌ Chemistry LaTeX generation failed: {e}")
            # Fallback to rule-based processing
            return self._apply_chemistry_latex_rules(latex_text)

    def _create_chemistry_latex_prompt(self, latex_text: str) -> str:
        """Create prompt for chemistry LaTeX-only output"""
        return f"""Convert this chemistry expression to perfect LaTeX code. Return ONLY the LaTeX code, no explanations:

Input: {latex_text}
Perfect LaTeX:"""

    def _clean_chemistry_latex(self, latex_text: str) -> str:
        """Clean chemistry LaTeX output to remove any non-LaTeX content"""
        import re

        # Remove any explanatory text
        lines = latex_text.split('\n')
        latex_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                continue
            # Skip explanatory text
            if any(line.startswith(word) for word in ['Note:', 'Explanation:', 'Here', 'The', 'This', 'Perfect', 'Output:']):
                continue
            # Keep lines that look like LaTeX
            if '\\' in line or any(char in line for char in '{}^_=+-*/()[]'):
                latex_lines.append(line)

        result = ' '.join(latex_lines).strip()

        # Apply chemistry-specific formatting
        result = self._apply_chemistry_latex_rules(result)

        return result if result else latex_text

    def _apply_chemistry_latex_rules(self, latex_text: str) -> str:
        """Apply rule-based chemistry LaTeX formatting"""
        import re

        # Chemical element formatting
        latex_text = re.sub(r'\b([A-Z][a-z]?)(\d+)\b', r'\\mathrm{\1}_{\2}', latex_text)
        latex_text = re.sub(r'\b([A-Z][a-z]?)\b', r'\\mathrm{\1}', latex_text)

        # Reaction arrows
        latex_text = re.sub(r'-->', r' \\rightarrow ', latex_text)
        latex_text = re.sub(r'->', r' \\rightarrow ', latex_text)
        latex_text = re.sub(r'<->', r' \\leftrightarrow ', latex_text)

        # Charges
        latex_text = re.sub(r'(\w+)([+-]+)', r'\1^{\2}', latex_text)

        # Clean spacing
        latex_text = re.sub(r'\s+', ' ', latex_text).strip()

        return latex_text

    def get_model_info(self):
        """Get information about the loaded model"""
        if self.is_available():
            return {
                'name': 'Qwen2.5-1.5B Text Processor',
                'model': self.model_name,
                'device': getattr(self, 'device', 'unknown'),
                'status': 'ready',
                'capabilities': ['text_enhancement', 'latex_formatting', 'mathematical_reasoning']
            }
        else:
            return {
                'name': 'Qwen2.5-1.5B Text Processor',
                'model': self.model_name,
                'device': 'none',
                'status': 'not available',
                'capabilities': []
            }

if __name__ == "__main__":
    # Test the Qwen2.5-1.5B processor
    processor = Qwen25TextProcessor()
    
    if processor.is_available():
        print("✅ Qwen2.5-1.5B processor is ready!")
        
        # Test enhancement
        test_latex = "x^2 + 2x + 1 = 0"
        result = processor.enhance_latex(test_latex, "Mathematics")
        
        print(f"📊 Test result:")
        print(f"   Original: {test_latex}")
        print(f"   Enhanced: {result['latex']}")
        print(f"   Confidence: {result['confidence']}%")
        print(f"   Improvements: {result['improvements']}")
    else:
        print("❌ Qwen2.5-1.5B processor is not available")
