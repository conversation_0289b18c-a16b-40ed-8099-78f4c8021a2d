#!/usr/bin/env python3
"""
Advanced Chemistry Processing Test - Test the improved chemistry processing
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_advanced_chemistry():
    """Test advanced chemistry processing with complex formulas"""
    
    # Import main app to test chemistry processing
    try:
        from main import LaTeXExtractorByYark

        # Create a minimal app instance
        app = LaTeXExtractorByYark()
        
        # Advanced test cases that might come from OCR
        test_cases = [
            # Complex formulas with parentheses
            "Ca ( OH ) 2 + 2 H Cl -> Ca Cl 2 + 2 H 2 O",
            "Ca(OH)2 + 2HCl -> CaCl2 + 2H2O",
            
            # Polyatomic ions
            "Ba ( NO 3 ) 2 + Na 2 SO 4 -> Ba SO 4 + 2 Na NO 3",
            "Ba(NO3)2 + Na2SO4 -> BaSO4 + 2NaNO3",
            
            # Organic chemistry
            "C H 4 + 2 O 2 -> C O 2 + 2 H 2 O",
            "CH4 + 2O2 -> CO2 + 2H2O",
            
            # Complex organic molecules
            "C 2 H 5 OH + 3 O 2 -> 2 C O 2 + 3 H 2 O",
            "C2H5OH + 3O2 -> 2CO2 + 3H2O",
            
            # Ionic equations with charges
            "Ag + + Cl - -> Ag Cl",
            "Ag+ + Cl- -> AgCl",
            
            # Acid-base reactions
            "H 2 SO 4 + 2 Na OH -> Na 2 SO 4 + 2 H 2 O",
            "H2SO4 + 2NaOH -> Na2SO4 + 2H2O",
            
            # Redox reactions
            "2 Fe + 3 Cl 2 -> 2 Fe Cl 3",
            "2Fe + 3Cl2 -> 2FeCl3",
            
            # Precipitation reactions
            "Pb ( NO 3 ) 2 + 2 K I -> Pb I 2 + 2 K NO 3",
            "Pb(NO3)2 + 2KI -> PbI2 + 2KNO3",
        ]
        
        print("🧪 Advanced Chemistry Processing Test")
        print("=" * 60)
        
        for i, test_input in enumerate(test_cases, 1):
            print(f"\n📝 Test {i}: {test_input}")
            
            # Apply the full chemistry processing pipeline
            result = app.apply_chemistry_latex_fixes(test_input)
            print(f"🧪 Result: {result}")
            
            # Check if result looks properly formatted
            has_mathrm = '\\mathrm{' in result
            has_arrows = '\\rightarrow' in result or '\\leftrightarrow' in result
            has_subscripts = '_{' in result
            
            quality_score = 0
            if has_mathrm:
                quality_score += 1
            if has_arrows:
                quality_score += 1
            if has_subscripts:
                quality_score += 1
                
            print(f"   Quality: {quality_score}/3 ({'✅' if quality_score >= 2 else '⚠️'})")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

def test_ocr_spacing_fix():
    """Test the OCR spacing fix specifically"""
    
    try:
        from main import LaTeXExtractorByYark

        app = LaTeXExtractorByYark()
        
        print("\n🔧 OCR Spacing Fix Test")
        print("=" * 40)
        
        spacing_tests = [
            "H 2 O",
            "Na Cl",
            "Ca ( OH ) 2",
            "C H 4",
            "SO 4",
            "NO 3",
            "Na + + Cl -",
            "- >",
            "< - >",
        ]
        
        for test in spacing_tests:
            result = app.fix_chemistry_ocr_spacing(test)
            print(f"'{test}' → '{result}'")
            
    except Exception as e:
        print(f"❌ OCR spacing test failed: {e}")

def test_ai_chemistry_processing():
    """Test AI chemistry processing if available"""
    
    try:
        from main import LaTeXExtractorByYark

        app = LaTeXExtractorByYark()
        
        print("\n🤖 AI Chemistry Processing Test")
        print("=" * 40)
        
        # Test if AI is available
        ai_enabled = app.settings.get('ai_enabled', False)
        subject_processing = app.settings.get('ai_subject_processing_enabled', False)
        
        print(f"AI Enabled: {ai_enabled}")
        print(f"Subject Processing: {subject_processing}")
        
        if ai_enabled and subject_processing:
            test_input = "H2O + NaCl -> Na+ + Cl- + H2O"
            result = app.ai_chemistry_processor(test_input)
            print(f"AI Result: {result}")
        else:
            print("AI processing not enabled - using rule-based processing")
            
    except Exception as e:
        print(f"❌ AI chemistry test failed: {e}")

if __name__ == "__main__":
    print("🔬 Advanced Chemistry Processing Test Suite")
    print("=" * 60)
    
    test_advanced_chemistry()
    test_ocr_spacing_fix()
    test_ai_chemistry_processing()
    
    print("\n✅ Advanced chemistry processing tests completed!")
