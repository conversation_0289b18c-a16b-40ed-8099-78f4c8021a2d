"""
Tests for OCR processing functionality in MathCapture Studio
"""

import unittest
import os
import cv2
import numpy as np
from PIL import Image

# Import the OCR processor from components
from components.ocr_processor import MathOCRProcessor

class TestOCRProcessing(unittest.TestCase):
    
    def setUp(self):
        """Set up test environment"""
        self.ocr_processor = MathOCRProcessor()
        
        # Create a simple test image with mathematical symbols
        self.test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        cv2.putText(self.test_image, "∫x^2 dx", (10, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    def test_basic_ocr(self):
        """Test basic OCR functionality"""
        # Convert to PIL Image
        pil_img = Image.fromarray(self.test_image)
        
        # Process with OCR
        result = self.ocr_processor.process_image(pil_img)
        
        # Basic validation
        self.assertIsNotNone(result)
        self.assertIsInstance(result, dict)
        self.assertIn('text', result)
        self.assertIn('confidence', result)
        
    def test_math_symbols(self):
        """Test recognition of mathematical symbols"""
        pil_img = Image.fromarray(self.test_image)
        result = self.ocr_processor.process_image(pil_img)
        
        # Check for integral symbol
        self.assertIn('∫', result['text'])
        
if __name__ == '__main__':
    unittest.main()