#!/usr/bin/env python3
"""
Quick test for Qwen-VL Vision AI integration in MathCapture Studio
Tests the integration without requiring full model download
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont

# Add the components directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'components'))

def test_qwen_vl_dependencies():
    """Test if all Qwen-VL dependencies are available"""
    print("🧪 Testing Qwen-VL Dependencies...")
    print("=" * 50)
    
    dependencies = [
        ('transformers', 'Hugging Face Transformers'),
        ('torch', 'PyTorch'),
        ('qwen_vl_utils', 'Qwen-VL Utils'),
        ('matplotlib', 'Matplotlib'),
        ('tiktoken', 'TikToken')
    ]
    
    all_available = True
    
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"✅ {name} available")
        except ImportError:
            print(f"❌ {name} missing - install with: pip install {module}")
            all_available = False
    
    return all_available

def test_qwen_vl_initialization():
    """Test Qwen-VL processor initialization (without model loading)"""
    print("\n🧪 Testing Qwen-VL Initialization...")
    print("=" * 50)
    
    try:
        from qwen_vl_processor import QwenVLProcessor
        
        # Create a mock processor that doesn't load the model
        class MockQwenVLProcessor(QwenVLProcessor):
            def _initialize_model(self):
                """Mock model initialization"""
                print("🔄 Mock: Skipping actual model loading for quick test")
                self.model_loaded = False  # Don't actually load
                self.device = "cpu"
        
        print("1. Creating mock processor...")
        processor = MockQwenVLProcessor()
        
        print("2. Testing processor methods...")
        
        # Test dependency check
        deps_ok = processor._check_dependencies()
        print(f"   Dependencies check: {'✅ Pass' if deps_ok else '❌ Fail'}")
        
        # Test prompt creation
        subjects = ["Mathematics", "Chemistry", "Physics"]
        for subject in subjects:
            prompt = processor._create_vision_prompt(subject)
            if prompt and len(prompt) > 100:
                print(f"   {subject} prompt: ✅ Generated ({len(prompt)} chars)")
            else:
                print(f"   {subject} prompt: ❌ Failed")
        
        # Test image preparation
        test_image = Image.new('RGB', (100, 50), color='white')
        image_path = processor._prepare_image(test_image)
        if image_path and os.path.exists(image_path):
            print(f"   Image preparation: ✅ Success ({image_path})")
            # Clean up
            try:
                os.unlink(image_path)
            except:
                pass
        else:
            print("   Image preparation: ❌ Failed")
        
        print("3. Mock processor test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing processor: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_app_integration():
    """Test integration with main application"""
    print("\n🧪 Testing Main Application Integration...")
    print("=" * 50)
    
    try:
        # Test importing the main app without starting GUI
        sys.path.append('.')
        
        # Mock the GUI parts to avoid starting the interface
        import tkinter as tk
        original_mainloop = tk.Tk.mainloop
        tk.Tk.mainloop = lambda self: None  # Mock mainloop
        
        from main import MathCaptureStudio
        
        print("1. Initializing MathCapture Studio...")
        app = MathCaptureStudio()
        
        print("2. Checking OCR processor availability...")
        
        # Check Qwen-VL
        qwen_available = (hasattr(app, 'qwen_vl') and 
                         app.qwen_vl and 
                         hasattr(app.qwen_vl, 'is_available'))
        
        if qwen_available:
            print("   ✅ Qwen-VL processor integrated")
            print(f"   Status: {'Ready' if app.qwen_vl.is_available() else 'Not loaded (expected for quick test)'}")
        else:
            print("   ❌ Qwen-VL processor not integrated")
        
        # Check LaTeX-OCR fallback
        latex_ocr_available = (hasattr(app, 'latex_ocr') and 
                              app.latex_ocr and 
                              app.latex_ocr.is_available())
        
        if latex_ocr_available:
            print("   ✅ LaTeX-OCR fallback available")
        else:
            print("   ⚠️ LaTeX-OCR fallback not available")
        
        # Check processing priority
        print("3. Testing OCR processing priority...")
        if hasattr(app, 'run_ocr_with_method'):
            print("   ✅ OCR processing method exists")
            print("   Priority: Qwen-VL → LaTeX-OCR (as expected)")
        else:
            print("   ❌ OCR processing method missing")
        
        # Restore original mainloop
        tk.Tk.mainloop = original_mainloop
        
        print("4. Main application integration test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing main application: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all quick tests"""
    print("🚀 Qwen-VL Quick Integration Test Suite")
    print("=" * 60)
    print("📝 This test verifies integration without downloading the full model")
    print()
    
    # Test 1: Dependencies
    deps_ok = test_qwen_vl_dependencies()
    
    # Test 2: Processor initialization
    init_ok = test_qwen_vl_initialization()
    
    # Test 3: Main app integration
    app_ok = test_main_app_integration()
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 60)
    print(f"Dependencies: {'✅ Pass' if deps_ok else '❌ Fail'}")
    print(f"Processor Init: {'✅ Pass' if init_ok else '❌ Fail'}")
    print(f"App Integration: {'✅ Pass' if app_ok else '❌ Fail'}")
    
    if deps_ok and init_ok and app_ok:
        print("\n🎉 All tests passed! Qwen-VL integration is ready.")
        print("💡 To use Qwen-VL:")
        print("   1. Run the main application: python main.py")
        print("   2. The first time will download the model (~10GB)")
        print("   3. Subsequent runs will be much faster")
        print("   4. If Qwen-VL fails, LaTeX-OCR will be used automatically")
    else:
        print("\n⚠️ Some tests failed. Check the output above for details.")
        print("💡 The application will still work with LaTeX-OCR fallback.")

if __name__ == "__main__":
    main()
