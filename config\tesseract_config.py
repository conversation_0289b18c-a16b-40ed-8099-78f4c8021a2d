"""
Tesseract OCR configuration for LaTeX Extractor by <PERSON><PERSON>
"""
import os
import platform
import shutil

# Default Tesseract configuration parameters
DEFAULT_CONFIG = {
    'psm': 6,  # Page segmentation mode
    'oem': 3,  # OCR Engine mode
    'char_whitelist': '0123456789+-*/=()[]{}^_abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ∫∑√∞αβγδεζηθικλμνξοπρστυφχψω',
    'preserve_interword_spaces': '1'
}

# Language configurations
LANGUAGES = {
    'math': {
        'traineddata': 'math.traineddata',
        'config': DEFAULT_CONFIG
    },
    'eng': {
        'traineddata': 'eng.traineddata',
        'config': {**DEFAULT_CONFIG, 'psm': 3}
    }
}

def find_tesseract_executable():
    """
    Find Tesseract executable on the system
    Returns the path to tesseract executable or None if not found
    """
    if platform.system() == 'Windows':
        # Common Tesseract installation paths on Windows
        possible_paths = [
            r'C:\Program Files\Tesseract-OCR\tesseract.exe',
            r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
            r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME')),
            r'C:\tesseract\tesseract.exe'
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path

    # Try to find tesseract in PATH (works for all platforms)
    tesseract_path = shutil.which('tesseract')
    return tesseract_path

def configure_pytesseract():
    """
    Configure pytesseract with the correct Tesseract executable path
    Returns True if successful, False otherwise
    """
    try:
        import pytesseract

        tesseract_path = find_tesseract_executable()
        if tesseract_path:
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
            # Test if it works
            pytesseract.get_tesseract_version()
            return True
        return False
    except Exception:
        return False