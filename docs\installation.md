# MathCapture Studio Installation Guide

## System Requirements

- **Operating System**: Windows 10/11, macOS 10.15+, or Linux
- **Python**: Version 3.8 or newer
- **Tesseract OCR**: Version 5.0 or newer

## Installation Steps

### 1. Install Python

Download and install Python from the [official website](https://www.python.org/downloads/).

### 2. Install Tesseract OCR

#### Windows
1. Download installer from [UB Mannheim](https://github.com/UB-Mannheim/tesseract/wiki)
2. Run the installer
3. Add Tesseract to your PATH:
   - Open System Properties > Environment Variables
   - Add `C:\Program Files\Tesseract-OCR` to PATH

#### macOS
```bash
brew install tesseract
```

#### Linux (Debian/Ubuntu)
```bash
sudo apt install tesseract-ocr
```

### 3. Install LaTeX Extractor by Yark

1. Clone the repository:
```bash
git clone https://github.com/yourusername/latex-extractor-by-yark.git
cd latex-extractor-by-yark
```

2. Install Python dependencies:
```bash
pip install -r requirements.txt
```

### 4. Run the Application
```bash
python main.py
```

## Post-Installation

### Configuration

- Edit `config/default_settings.json` to customize application settings
- Place custom templates in `assets/templates/`

### Verifying Installation

Run the test suite to verify everything works:
```bash
python -m unittest discover tests
```

## Updating

To update MathCapture Studio:
```bash
git pull origin main
pip install --upgrade -r requirements.txt
```