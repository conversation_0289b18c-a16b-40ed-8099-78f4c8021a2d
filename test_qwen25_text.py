#!/usr/bin/env python3
"""
Test script for Qwen2.5-1.5B Text Processor integration in MathCapture Studio
This tests the intelligent LaTeX enhancement capabilities
"""

import os
import sys
import time

# Add the components directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'components'))

def test_qwen25_text_processor():
    """Test the Qwen2.5-1.5B text processor"""
    print("🧠 Testing Qwen2.5-1.5B Text Processor...")
    print("=" * 60)
    
    try:
        # Import the processor
        from qwen25_text_processor import Qwen25TextProcessor
        
        # Initialize the processor
        print("1. Initializing Qwen2.5-1.5B text processor...")
        processor = Qwen25TextProcessor()
        
        # Check if it's available
        print(f"2. Processor available: {processor.is_available()}")
        
        if processor.is_available():
            print("3. Model info:")
            model_info = processor.get_model_info()
            for key, value in model_info.items():
                print(f"   {key}: {value}")
            
            # Test LaTeX enhancement with different subjects
            test_cases = [
                {
                    "subject": "Mathematics",
                    "latex": "x^2 + 2x + 1 = 0",
                    "description": "Simple quadratic equation"
                },
                {
                    "subject": "Chemistry", 
                    "latex": "H2 + O2 -> H2O",
                    "description": "Chemical reaction"
                },
                {
                    "subject": "Physics",
                    "latex": "F = ma",
                    "description": "Newton's second law"
                },
                {
                    "subject": "Mathematics",
                    "latex": "int_0^infty e^(-x^2) dx",
                    "description": "Gaussian integral (needs LaTeX formatting)"
                },
                {
                    "subject": "Chemistry",
                    "latex": "2H2SO4 + Ca(OH)2 -> CaSO4 + 2H2O",
                    "description": "Acid-base reaction (needs chemical formatting)"
                }
            ]
            
            print("4. Testing LaTeX enhancement...")
            for i, test_case in enumerate(test_cases, 1):
                print(f"\n   Test {i}: {test_case['description']}")
                print(f"   Subject: {test_case['subject']}")
                print(f"   Original: {test_case['latex']}")
                
                start_time = time.time()
                result = processor.enhance_latex(test_case['latex'], test_case['subject'])
                process_time = time.time() - start_time
                
                if result:
                    print(f"   ✅ Enhanced: {result['latex']}")
                    print(f"   Confidence: {result['confidence']}%")
                    print(f"   Improvements: {', '.join(result['improvements'])}")
                    print(f"   Processing time: {process_time:.2f} seconds")
                    
                    # Performance assessment
                    if process_time < 5:
                        print("   🚀 Excellent processing speed!")
                    elif process_time < 15:
                        print("   ✅ Good processing speed")
                    else:
                        print("   ⚠️ Slower than expected")
                else:
                    print(f"   ❌ Enhancement failed")
            
            return True
            
        else:
            print("❌ Qwen2.5-1.5B processor is not available")
            print("💡 This could be due to:")
            print("   - Missing dependencies (transformers>=4.37.0)")
            print("   - Model download issues")
            print("   - Insufficient memory")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install transformers>=4.37.0")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_with_main_app():
    """Test integration with main application"""
    print("\n🧪 Testing Integration with MathCapture Studio...")
    print("=" * 60)
    
    try:
        # Mock the GUI parts to avoid starting the interface
        import tkinter as tk
        original_mainloop = tk.Tk.mainloop
        tk.Tk.mainloop = lambda self: None  # Mock mainloop
        
        sys.path.append('.')
        from main import MathCaptureStudio
        
        print("1. Initializing MathCapture Studio...")
        app = MathCaptureStudio()
        
        print("2. Checking Qwen2.5-1.5B integration...")
        
        # Check Qwen2.5-1.5B integration
        qwen25_available = (hasattr(app, 'qwen25_text') and 
                           app.qwen25_text and 
                           hasattr(app.qwen25_text, 'is_available'))
        
        if qwen25_available:
            print("   ✅ Qwen2.5-1.5B text processor integrated")
            model_info = app.qwen25_text.get_model_info()
            print(f"   Model: {model_info.get('name', 'Unknown')}")
            print(f"   Status: {model_info.get('status', 'Unknown')}")
            
            if app.qwen25_text.is_available():
                print("   ✅ Qwen2.5-1.5B is ready for LaTeX enhancement")
            else:
                print("   ⚠️ Qwen2.5-1.5B integrated but not loaded")
        else:
            print("   ❌ Qwen2.5-1.5B text processor not integrated")
        
        # Check other processors
        latex_ocr_available = (hasattr(app, 'latex_ocr') and 
                              app.latex_ocr and 
                              app.latex_ocr.is_available())
        
        qwen_vl_available = (hasattr(app, 'qwen_vl') and 
                            app.qwen_vl and 
                            hasattr(app.qwen_vl, 'is_available'))
        
        print("3. Checking complete processing pipeline...")
        print(f"   LaTeX-OCR: {'✅ Available' if latex_ocr_available else '❌ Not available'}")
        print(f"   Qwen-VL: {'✅ Available' if qwen_vl_available else '⚠️ Not available'}")
        print(f"   Qwen2.5-1.5B: {'✅ Available' if qwen25_available else '❌ Not available'}")
        
        # Restore original mainloop
        tk.Tk.mainloop = original_mainloop
        
        print("4. Integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing integration: {e}")
        return False

def test_memory_usage():
    """Test memory usage of Qwen2.5-1.5B"""
    print("\n🧪 Testing Memory Usage...")
    print("=" * 60)
    
    try:
        import psutil
        
        # Get initial memory
        process = psutil.Process()
        initial_memory = process.memory_info().rss / (1024**3)
        
        print(f"1. Initial memory usage: {initial_memory:.2f} GB")
        
        # Load processor
        from qwen25_text_processor import Qwen25TextProcessor
        processor = Qwen25TextProcessor()
        
        # Get memory after loading
        final_memory = process.memory_info().rss / (1024**3)
        memory_increase = final_memory - initial_memory
        
        print(f"2. Memory after loading: {final_memory:.2f} GB")
        print(f"3. Memory increase: {memory_increase:.2f} GB")
        
        # Memory efficiency assessment
        if memory_increase < 2:
            print("   ✅ Excellent memory efficiency")
        elif memory_increase < 4:
            print("   ✅ Good memory efficiency")
        else:
            print("   ⚠️ High memory usage")
        
        # Test processing memory
        if processor.is_available():
            test_latex = "\\int_0^\\infty e^{-x^2} dx = \\frac{\\sqrt{\\pi}}{2}"
            result = processor.enhance_latex(test_latex, "Mathematics")
            
            processing_memory = process.memory_info().rss / (1024**3)
            processing_increase = processing_memory - final_memory
            
            print(f"4. Memory during processing: {processing_memory:.2f} GB")
            print(f"5. Processing memory increase: {processing_increase:.2f} GB")
        
        return True
        
    except ImportError:
        print("⚠️ psutil not available - install with: pip install psutil")
        return False
    except Exception as e:
        print(f"❌ Memory test failed: {e}")
        return False

def main():
    """Run all tests for Qwen2.5-1.5B text processor"""
    print("🚀 Qwen2.5-1.5B Text Processor Test Suite")
    print("=" * 70)
    print("📝 Testing intelligent LaTeX enhancement capabilities")
    print()
    
    # Test 1: Qwen2.5-1.5B processor
    processor_ok = test_qwen25_text_processor()
    
    # Test 2: Integration with main app
    integration_ok = test_integration_with_main_app()
    
    # Test 3: Memory usage
    memory_ok = test_memory_usage()
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 70)
    print(f"Qwen2.5-1.5B Processor: {'✅ Working' if processor_ok else '❌ Failed'}")
    print(f"Integration: {'✅ Working' if integration_ok else '❌ Failed'}")
    print(f"Memory Usage: {'✅ Efficient' if memory_ok else '⚠️ Check'}")
    
    if processor_ok and integration_ok:
        print("\n🎉 Qwen2.5-1.5B is working perfectly!")
        print("💡 What this means:")
        print("   ✅ Intelligent LaTeX enhancement is active")
        print("   ✅ Subject-specific processing for Math/Chemistry/Physics")
        print("   ✅ Improved accuracy and formatting")
        print("   ✅ Works with your current system memory")
        print()
        print("🚀 Ready for enhanced mathematical processing!")
        print("   - LaTeX-OCR provides base recognition")
        print("   - Qwen2.5-1.5B enhances and perfects the output")
        print("   - Combined system delivers professional quality")
        
    elif integration_ok:
        print("\n✅ System is working with basic processing!")
        print("💡 What this means:")
        print("   ✅ LaTeX-OCR provides excellent mathematical OCR")
        print("   ⚠️ Qwen2.5-1.5B enhancement not available")
        print("   ✅ Still delivers high-quality results")
        print()
        print("🚀 Ready for production use with reliable processing!")
        
    else:
        print("\n❌ Issues detected - please check the errors above")
        
    print("\n💡 Usage Instructions:")
    print("   - Run 'python main.py' to start MathCapture Studio")
    print("   - Select image regions and click 'Process OCR'")
    print("   - The system will automatically enhance LaTeX with Qwen2.5-1.5B")
    print("   - Enhanced processing provides better formatting and accuracy")

if __name__ == "__main__":
    main()
