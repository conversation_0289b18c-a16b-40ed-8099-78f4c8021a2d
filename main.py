import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk

import fitz  # PyMuPDF
from pdf2image import convert_from_path
import os
import json
from dataclasses import dataclass, asdict
from typing import List, Tuple, Optional
import threading
from docx import Document
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls
import re

# Try to import LaTeX-OCR processor
try:
    from components.latex_ocr_processor import LaTeXOCRProcessor
    LATEX_OCR_AVAILABLE = True
except ImportError:
    LATEX_OCR_AVAILABLE = False
    LaTeXOCRProcessor = None

@dataclass
class EquationRegion:
    """Represents a selected equation region"""
    x: int
    y: int
    width: int
    height: int
    page_num: int
    filename: str
    latex_text: str = ""
    confidence: float = 0.0

@dataclass
class ProjectSession:
    """Represents a saved project session"""
    files: List[str]
    equations: List[EquationRegion]
    settings: dict

class LaTeXExtractorByYark:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("LaTeX Extractor by Yark")
        self.root.geometry("1400x900")

        # Application state
        self.current_files = []
        self.current_images = []
        self.current_page = 0
        self.equation_regions = []
        self.equation_queue = []
        self.selection_start = None
        self.selection_rect = None

        # Zoom state
        self.zoom_factor = 1.0
        self.original_image = None

        # Settings
        self.settings = {
            'export_dir': os.path.expanduser('~/Documents'),
            'font_family': 'Times New Roman',
            'font_size': 12,
            'inline_equations': False,
            'show_page_refs': True,
            'debug_files': True,  # Enable debug for troubleshooting
            'ocr_method': 'latex_ocr',  # Only LaTeX-OCR now

            # AI Settings - Enable AI for better chemistry processing
            'ai_enabled': True,  # Master AI toggle - enable for better results
            'ai_vision_enabled': False,  # Qwen-VL Vision AI for OCR (keep disabled for performance)
            'ai_text_enhancement_enabled': True,  # Qwen2.5-1.5B for LaTeX enhancement
            'ai_subject_processing_enabled': True,  # AI-powered subject-specific processing
            'ai_math_reasoning_enabled': True,  # AI Math Processor reasoning
            'ai_show_status': True,  # Show AI status indicators in UI
        }

        # Initialize current subject
        self.current_subject = "Mathematics"

        self.setup_latex_ocr()
        self.setup_ui()



    def setup_latex_ocr(self):
        """Initialize OCR and AI components based on settings"""
        # Always initialize LaTeX-OCR (core functionality)
        self.latex_ocr = None
        if LATEX_OCR_AVAILABLE:
            try:
                print("Initializing LaTeX-OCR...")
                self.latex_ocr = LaTeXOCRProcessor()
                if self.latex_ocr.is_available():
                    print("✅ LaTeX-OCR ready for mathematical image-to-LaTeX conversion!")
                else:
                    print("⚠️  LaTeX-OCR initialization failed")
            except Exception as e:
                print(f"❌ LaTeX-OCR setup error: {e}")
                self.latex_ocr = None
        else:
            print("❌ LaTeX-OCR not available. Please install pix2tex for mathematical OCR.")

        # Initialize AI components only if enabled
        ai_enabled = self.settings.get('ai_enabled', False)

        # Vision AI initialization
        if ai_enabled and self.settings.get('ai_vision_enabled', False):
            print("AI enabled - initializing Vision AI components...")
            # Skip Qwen-VL (removed by user request - too heavy for current system)
            print("Skipping Qwen-VL Vision AI (removed for better performance)")
            self.qwen_vl = None
        else:
            print("🚀 Vision AI disabled - skipping for faster startup")
            self.qwen_vl = None

        # Text enhancement AI initialization
        if ai_enabled and self.settings.get('ai_text_enhancement_enabled', False):
            print("AI enabled - initializing Qwen2.5-1.5B Text Processor...")
            try:
                from components.qwen25_text_processor import Qwen25TextProcessor
                self.qwen25_text = Qwen25TextProcessor()

                if self.qwen25_text.is_available():
                    print("✅ Qwen2.5-1.5B ready for intelligent LaTeX enhancement!")
                else:
                    print("⚠️ Qwen2.5-1.5B not available - using basic LaTeX processing")

            except Exception as e:
                print(f"⚠️ Qwen2.5-1.5B initialization failed: {e}")
                print("🔄 Will use basic LaTeX processing")
                self.qwen25_text = None
        else:
            print("🚀 Text enhancement AI disabled - skipping for faster startup")
            self.qwen25_text = None

        # Print startup summary
        if ai_enabled:
            enabled_features = []
            if self.settings.get('ai_vision_enabled', False):
                enabled_features.append("Vision")
            if self.settings.get('ai_text_enhancement_enabled', False):
                enabled_features.append("Enhancement")
            if self.settings.get('ai_subject_processing_enabled', False):
                enabled_features.append("Subject")
            if self.settings.get('ai_math_reasoning_enabled', False):
                enabled_features.append("Reasoning")

            if enabled_features:
                print(f"🤖 AI Features Enabled: {', '.join(enabled_features)}")
            else:
                print("🤖 AI Enabled but no features selected")
        else:
            print("🚀 AI Disabled - Fast Mode Active (faster startup and processing)")

    def setup_ui(self):
        """Setup the main UI layout"""
        # Create main frames
        self.create_menu()
        self.create_toolbar()
        self.create_main_layout()

    def create_menu(self):
        """Create application menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Import PDF/Images", command=self.import_files)
        file_menu.add_command(label="Save Project", command=self.save_project)
        file_menu.add_command(label="Load Project", command=self.load_project)
        file_menu.add_separator()
        file_menu.add_command(label="Export to Word", command=self.export_to_word)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)

        # Settings menu
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Settings", menu=settings_menu)
        settings_menu.add_command(label="Preferences", command=self.open_settings)

    def create_toolbar(self):
        """Create main toolbar"""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)

        ttk.Button(toolbar, text="Import Files", command=self.import_files).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Export to Word", command=self.export_to_word).pack(side=tk.LEFT, padx=2)
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        ttk.Button(toolbar, text="Settings", command=self.open_settings).pack(side=tk.LEFT, padx=2)

        # AI Status Indicator
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        self.ai_status_label = ttk.Label(toolbar, text="", foreground="gray")
        self.ai_status_label.pack(side=tk.LEFT, padx=5)

        # Update AI status display
        self._update_ai_status_display()

    def create_main_layout(self):
        """Create the main application layout with tabs for different subjects"""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Create tabs for different subjects
        self.create_mathematics_tab()
        self.create_chemistry_tab()
        self.create_physics_tab()

        # Bind tab change event
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

    def create_mathematics_tab(self):
        """Create Mathematics tab"""
        math_frame = ttk.Frame(self.notebook)
        self.notebook.add(math_frame, text="📐 Mathematics")

        # Store reference for current active tab
        self.math_frame = math_frame

        # Create the standard layout for mathematics
        self.create_subject_layout(math_frame, "Mathematics")

    def create_chemistry_tab(self):
        """Create Chemistry tab"""
        chem_frame = ttk.Frame(self.notebook)
        self.notebook.add(chem_frame, text="🧪 Chemistry")

        # Store reference
        self.chem_frame = chem_frame

        # Create layout for chemistry
        self.create_subject_layout(chem_frame, "Chemistry")

    def create_physics_tab(self):
        """Create Physics tab"""
        phys_frame = ttk.Frame(self.notebook)
        self.notebook.add(phys_frame, text="⚛️ Physics")

        # Store reference
        self.phys_frame = phys_frame

        # Create layout for physics
        self.create_subject_layout(phys_frame, "Physics")

    def create_subject_layout(self, parent_frame, subject):
        """Create the layout for a specific subject tab"""
        # Create paned windows for resizable layout
        h_paned = ttk.PanedWindow(parent_frame, orient=tk.HORIZONTAL)
        h_paned.pack(fill=tk.BOTH, expand=True)

        # Left panel - Separate file import section for each subject
        self.create_subject_file_panel(h_paned, subject)

        # Center and right panel container
        center_right_frame = ttk.Frame(h_paned)
        h_paned.add(center_right_frame, weight=3)

        v_paned = ttk.PanedWindow(center_right_frame, orient=tk.VERTICAL)
        v_paned.pack(fill=tk.BOTH, expand=True)

        # Top section with preview and editor
        top_frame = ttk.Frame(v_paned)
        v_paned.add(top_frame, weight=3)

        h_paned2 = ttk.PanedWindow(top_frame, orient=tk.HORIZONTAL)
        h_paned2.pack(fill=tk.BOTH, expand=True)

        # Center - Separate Preview & Region Selection for each subject
        self.create_subject_preview_panel(h_paned2, subject)

        # Right - Subject-specific Editor
        self.create_subject_editor_panel(h_paned2, subject)

        # Bottom - Subject-specific Queue
        self.create_subject_queue_panel(v_paned, subject)

    def create_subject_file_panel(self, parent, subject):
        """Create separate file panel for each subject"""
        icon = {"Mathematics": "📐", "Chemistry": "🧪", "Physics": "⚛️"}.get(subject, "📐")
        file_frame = ttk.LabelFrame(parent, text=f"{icon} {subject} Files", padding=5)
        parent.add(file_frame, weight=1)

        # Subject-specific import button
        ttk.Button(file_frame, text=f"Import {subject} Files",
                  command=lambda: self.import_subject_files(subject)).pack(fill=tk.X, pady=(0, 5))

        # Subject-specific file list
        list_frame = ttk.Frame(file_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # Create subject-specific file listbox
        file_listbox_attr = f"file_listbox_{subject.lower()}"
        setattr(self, file_listbox_attr, tk.Listbox(list_frame))
        file_listbox = getattr(self, file_listbox_attr)

        file_scroll = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=file_listbox.yview)
        file_listbox.config(yscrollcommand=file_scroll.set)

        file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        file_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        file_listbox.bind('<<ListboxSelect>>', lambda e: self.on_subject_file_select(e, subject))

        # Subject-specific navigation controls
        nav_frame = ttk.Frame(file_frame)
        nav_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(nav_frame, text="◀ Prev",
                  command=lambda: self.prev_subject_page(subject)).pack(side=tk.LEFT, padx=2)
        ttk.Button(nav_frame, text="Next ▶",
                  command=lambda: self.next_subject_page(subject)).pack(side=tk.LEFT, padx=2)

        # Subject-specific page label
        page_label_attr = f"page_label_{subject.lower()}"
        setattr(self, page_label_attr, ttk.Label(nav_frame, text="No files loaded"))
        getattr(self, page_label_attr).pack(side=tk.RIGHT)

        # Export button for this subject
        ttk.Button(file_frame, text=f"Export {subject} to Word",
                  command=lambda: self.export_subject_to_word(subject)).pack(fill=tk.X, pady=(5, 0))

    def create_subject_preview_panel(self, parent, subject):
        """Create separate preview panel for each subject"""
        icon = {"Mathematics": "📐", "Chemistry": "🧪", "Physics": "⚛️"}.get(subject, "📐")
        preview_frame = ttk.LabelFrame(parent, text=f"{icon} {subject} Preview & Selection", padding=5)
        parent.add(preview_frame, weight=2)

        # Canvas for image display
        canvas_frame = ttk.Frame(preview_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        # Create subject-specific canvas
        canvas_attr = f"canvas_{subject.lower()}"
        setattr(self, canvas_attr, tk.Canvas(canvas_frame, bg='white'))
        canvas = getattr(self, canvas_attr)

        h_scroll = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=canvas.xview)
        v_scroll = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=canvas.yview)
        canvas.config(xscrollcommand=h_scroll.set, yscrollcommand=v_scroll.set)

        canvas.grid(row=0, column=0, sticky='nsew')
        h_scroll.grid(row=1, column=0, sticky='ew')
        v_scroll.grid(row=0, column=1, sticky='ns')

        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)

        # Bind mouse events for region selection
        canvas.bind('<Button-1>', lambda e: self.start_subject_selection(e, subject))
        canvas.bind('<B1-Motion>', lambda e: self.update_subject_selection(e, subject))
        canvas.bind('<ButtonRelease-1>', lambda e: self.end_subject_selection(e, subject))

        # Bind mouse wheel for zoom
        canvas.bind('<MouseWheel>', lambda e: self.on_subject_mouse_wheel(e, subject))
        canvas.bind('<Control-Button-4>', lambda e: self.on_subject_mouse_wheel(e, subject))  # Linux
        canvas.bind('<Control-Button-5>', lambda e: self.on_subject_mouse_wheel(e, subject))  # Linux

        # Zoom controls
        zoom_frame = ttk.Frame(preview_frame)
        zoom_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(zoom_frame, text="Zoom In",
                  command=lambda: self.zoom_subject_in(subject)).pack(side=tk.LEFT, padx=2)
        ttk.Button(zoom_frame, text="Zoom Out",
                  command=lambda: self.zoom_subject_out(subject)).pack(side=tk.LEFT, padx=2)
        ttk.Button(zoom_frame, text="Fit",
                  command=lambda: self.fit_subject_to_window(subject)).pack(side=tk.LEFT, padx=2)

        # Subject-specific status
        status_label = ttk.Label(zoom_frame, text=f"{icon} {subject} Ready", foreground="darkgreen")
        status_label.pack(side=tk.RIGHT)



    def create_subject_editor_panel(self, parent, subject):
        """Create subject-specific equation editor panel"""
        editor_frame = ttk.LabelFrame(parent, text=f"{subject} Editor", padding=5)
        parent.add(editor_frame, weight=1)

        # Subject-specific instructions
        subject_instructions = {
            "Mathematics": "📐 Mathematical equations, formulas, and expressions",
            "Chemistry": "🧪 Chemical equations, molecular formulas, and reactions",
            "Physics": "⚛️ Physical laws, equations, and scientific notation"
        }

        ttk.Label(editor_frame, text=subject_instructions.get(subject, ""),
                 foreground="darkblue").pack(anchor=tk.W, pady=(0, 5))

        # OCR result display
        ttk.Label(editor_frame, text="Recognized Text:").pack(anchor=tk.W)

        # Create subject-specific text widgets
        ocr_attr = f"ocr_text_{subject.lower()}"
        latex_attr = f"latex_text_{subject.lower()}"

        setattr(self, ocr_attr, tk.Text(editor_frame, height=4, wrap=tk.WORD))
        getattr(self, ocr_attr).pack(fill=tk.X, pady=(0, 5))

        # LaTeX editor
        ttk.Label(editor_frame, text=f"{subject} LaTeX Editor:").pack(anchor=tk.W)
        setattr(self, latex_attr, tk.Text(editor_frame, height=6, wrap=tk.WORD))
        getattr(self, latex_attr).pack(fill=tk.X, pady=(0, 5))

        # Preview area
        ttk.Label(editor_frame, text="Preview:").pack(anchor=tk.W)
        preview_attr = f"preview_frame_{subject.lower()}"
        setattr(self, preview_attr, ttk.Frame(editor_frame, relief=tk.SUNKEN, borderwidth=1))
        getattr(self, preview_attr).pack(fill=tk.X, pady=(0, 5), ipady=20)

        # Subject-specific OCR status
        self.create_subject_ocr_status(editor_frame, subject)

        # Subject-specific buttons
        self.create_subject_buttons(editor_frame, subject)

    def create_subject_ocr_status(self, parent, subject):
        """Create subject-specific OCR status display"""
        ocr_frame = ttk.LabelFrame(parent, text=f"{subject} OCR Status", padding=5)
        ocr_frame.pack(fill=tk.X, pady=(0, 5))

        if self.latex_ocr and self.latex_ocr.is_available():
            status_text = f"✅ LaTeX-OCR Ready for {subject}"
            color = "green"
        else:
            status_text = f"❌ LaTeX-OCR Not Available for {subject}"
            color = "red"

        ttk.Label(ocr_frame, text=status_text, foreground=color).pack(side=tk.LEFT)

        # Chemistry tab just needs LaTeX output - no format selector needed

    def create_subject_buttons(self, parent, subject):
        """Create subject-specific action buttons"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X)

        # Process OCR button
        ttk.Button(button_frame, text=f"Process {subject} OCR",
                  command=lambda: self.process_subject_ocr(subject)).pack(side=tk.LEFT, padx=2)

        # Fix LaTeX button
        ttk.Button(button_frame, text=f"Fix {subject} LaTeX",
                  command=lambda: self.fix_subject_latex(subject)).pack(side=tk.LEFT, padx=2)

        # Add to Queue button
        ttk.Button(button_frame, text=f"Add to {subject} Queue",
                  command=lambda: self.add_to_subject_queue(subject)).pack(side=tk.LEFT, padx=2)

    def create_subject_queue_panel(self, parent, subject):
        """Create subject-specific equation queue panel"""
        queue_frame = ttk.LabelFrame(parent, text=f"{subject} Equation Queue", padding=5)
        parent.add(queue_frame, weight=1)

        # Queue list with scrollbar
        list_frame = ttk.Frame(queue_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # Create subject-specific treeview
        columns = ('Page', 'LaTeX', 'Confidence')
        queue_attr = f"queue_tree_{subject.lower()}"
        setattr(self, queue_attr, ttk.Treeview(list_frame, columns=columns, show='tree headings', height=6))

        queue_tree = getattr(self, queue_attr)
        queue_tree.heading('#0', text='#')
        queue_tree.heading('Page', text='Page')
        queue_tree.heading('LaTeX', text='LaTeX')
        queue_tree.heading('Confidence', text='Confidence')

        queue_tree.column('#0', width=50)
        queue_tree.column('Page', width=80)
        queue_tree.column('LaTeX', width=300)
        queue_tree.column('Confidence', width=100)

        queue_scroll = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=queue_tree.yview)
        queue_tree.config(yscrollcommand=queue_scroll.set)

        queue_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        queue_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Bind events
        queue_tree.bind('<Double-1>', lambda e: self.copy_latex_from_subject_queue(e, subject))
        queue_tree.bind('<Button-3>', lambda e: self.show_subject_queue_context_menu(e, subject))

        # Subject-specific queue controls
        self.create_subject_queue_controls(queue_frame, subject)

    def create_subject_queue_controls(self, parent, subject):
        """Create subject-specific queue controls"""
        queue_controls = ttk.Frame(parent)
        queue_controls.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(queue_controls, text=f"Clear {subject} Queue",
                  command=lambda: self.clear_subject_queue(subject)).pack(side=tk.LEFT, padx=2)
        ttk.Button(queue_controls, text=f"Export {subject}",
                  command=lambda: self.export_subject_equations(subject)).pack(side=tk.LEFT, padx=2)
        ttk.Button(queue_controls, text=f"Copy All {subject}",
                  command=lambda: self.copy_all_subject_latex(subject)).pack(side=tk.LEFT, padx=2)

    def on_tab_changed(self, event):
        """Handle tab change events"""
        selected_tab = event.widget.tab('current')['text']
        print(f"🔄 Switched to tab: {selected_tab}")

        # Update current subject
        if "Mathematics" in selected_tab:
            self.current_subject = "Mathematics"
        elif "Chemistry" in selected_tab:
            self.current_subject = "Chemistry"
        elif "Physics" in selected_tab:
            self.current_subject = "Physics"

        # Update window title
        self.root.title(f"LaTeX Extractor by Yark - {self.current_subject}")

        # Initialize subject-specific data if needed
        self.initialize_subject_data(self.current_subject)

    def get_current_subject(self):
        """Get the currently active subject tab"""
        if hasattr(self, 'current_subject'):
            return self.current_subject
        return "Mathematics"  # Default

    def get_subject_widgets(self, subject):
        """Get the text widgets for a specific subject"""
        ocr_attr = f"ocr_text_{subject.lower()}"
        latex_attr = f"latex_text_{subject.lower()}"
        queue_attr = f"queue_tree_{subject.lower()}"

        return {
            'ocr_text': getattr(self, ocr_attr, None),
            'latex_text': getattr(self, latex_attr, None),
            'queue_tree': getattr(self, queue_attr, None)
        }

    def process_subject_ocr(self, subject):
        """Process OCR for a specific subject"""
        print(f"🔥 Processing {subject} OCR...")

        # Use the same OCR processing but update subject-specific widgets
        self.current_subject = subject

        # Call the main OCR processing
        self.process_selected_region()

    def fix_subject_latex(self, subject):
        """Fix LaTeX for a specific subject"""
        print(f"🔧 Fixing {subject} LaTeX...")

        widgets = self.get_subject_widgets(subject)
        if not widgets['latex_text']:
            messagebox.showwarning("No LaTeX", f"No LaTeX editor found for {subject}")
            return

        # Get current LaTeX text from subject-specific editor
        current_latex = widgets['latex_text'].get(1.0, tk.END).strip()

        if not current_latex:
            messagebox.showwarning("No LaTeX", f"Please enter some LaTeX code first for {subject}.")
            return

        # Detect pitfalls and fix
        pitfalls = self.detect_latex_pitfalls(current_latex)
        fixed_latex = self.comprehensive_latex_fix(current_latex)

        # Update subject-specific widgets
        widgets['latex_text'].delete(1.0, tk.END)
        widgets['latex_text'].insert(1.0, fixed_latex)

        if widgets['ocr_text']:
            fixes_applied = self.get_fixes_applied(current_latex, fixed_latex)
            widgets['ocr_text'].delete(1.0, tk.END)
            widgets['ocr_text'].insert(1.0, f"[{subject} Fix] {fixes_applied}")

        # Show fix report
        self.show_fix_report(pitfalls, current_latex, fixed_latex)

    def add_to_subject_queue(self, subject):
        """Add equation to subject-specific queue"""
        print(f"➕ Adding to {subject} queue...")

        widgets = self.get_subject_widgets(subject)
        if not widgets['latex_text'] or not widgets['queue_tree']:
            messagebox.showwarning("Error", f"Queue not available for {subject}")
            return

        latex_text = widgets['latex_text'].get(1.0, tk.END).strip()
        if not latex_text:
            messagebox.showwarning("No LaTeX", f"No LaTeX to add to {subject} queue")
            return

        # Create equation region for this subject
        equation = EquationRegion(
            x=0, y=0, width=100, height=50,
            latex_text=latex_text,
            confidence=95.0,
            filename=f"{subject} Equation",
            page_num=1
        )

        # Add to subject-specific queue
        queue_attr = f"equation_queue_{subject.lower()}"
        if not hasattr(self, queue_attr):
            setattr(self, queue_attr, [])

        subject_queue = getattr(self, queue_attr)
        subject_queue.append(equation)

        # Update subject-specific queue display
        self.update_subject_queue_display(subject)

        messagebox.showinfo("Added", f"Equation added to {subject} queue!")

    def update_subject_queue_display(self, subject):
        """Update the display for a subject-specific queue"""
        widgets = self.get_subject_widgets(subject)
        queue_tree = widgets['queue_tree']

        if not queue_tree:
            return

        # Clear current items
        for item in queue_tree.get_children():
            queue_tree.delete(item)

        # Get subject-specific queue
        queue_attr = f"equation_queue_{subject.lower()}"
        subject_queue = getattr(self, queue_attr, [])

        # Add items to tree
        for i, eq in enumerate(subject_queue):
            queue_tree.insert('', 'end', text=str(i+1), values=(
                eq.filename,
                eq.latex_text[:50] + "..." if len(eq.latex_text) > 50 else eq.latex_text,
                f"{eq.confidence:.1f}%"
            ))

    def clear_subject_queue(self, subject):
        """Clear subject-specific queue"""
        queue_attr = f"equation_queue_{subject.lower()}"
        setattr(self, queue_attr, [])
        self.update_subject_queue_display(subject)
        messagebox.showinfo("Cleared", f"{subject} queue cleared!")

    def export_subject_equations(self, subject):
        """Export equations for a specific subject"""
        queue_attr = f"equation_queue_{subject.lower()}"
        subject_queue = getattr(self, queue_attr, [])

        if not subject_queue:
            messagebox.showwarning("Empty Queue", f"No {subject} equations to export")
            return

        # Use existing export functionality but filter by subject
        self.export_to_word(subject_filter=subject)

    def copy_all_subject_latex(self, subject):
        """Copy all LaTeX from subject-specific queue"""
        queue_attr = f"equation_queue_{subject.lower()}"
        subject_queue = getattr(self, queue_attr, [])

        if not subject_queue:
            messagebox.showwarning("Empty Queue", f"No {subject} equations to copy")
            return

        # Combine all LaTeX
        all_latex = "\n\n".join([eq.latex_text for eq in subject_queue])

        # Copy to clipboard
        self.root.clipboard_clear()
        self.root.clipboard_append(all_latex)

        messagebox.showinfo("Copied", f"All {subject} LaTeX copied to clipboard!")

    def copy_latex_from_subject_queue(self, event, subject):
        """Copy LaTeX from subject-specific queue"""
        widgets = self.get_subject_widgets(subject)
        queue_tree = widgets['queue_tree']

        if not queue_tree:
            return

        selection = queue_tree.selection()
        if selection:
            item = queue_tree.item(selection[0])
            latex_text = item['values'][1]  # LaTeX column

            self.root.clipboard_clear()
            self.root.clipboard_append(latex_text)
            messagebox.showinfo("Copied", f"{subject} LaTeX copied to clipboard!")

    def show_subject_queue_context_menu(self, event, subject):
        """Show context menu for subject-specific queue"""
        # Create context menu for subject-specific operations
        context_menu = tk.Menu(self.root, tearoff=0)
        context_menu.add_command(label=f"Copy {subject} LaTeX",
                               command=lambda: self.copy_latex_from_subject_queue(event, subject))
        context_menu.add_command(label=f"Remove from {subject} Queue",
                               command=lambda: self.remove_from_subject_queue(subject))

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def remove_from_subject_queue(self, subject):
        """Remove selected item from subject-specific queue"""
        widgets = self.get_subject_widgets(subject)
        queue_tree = widgets['queue_tree']

        if not queue_tree:
            return

        selection = queue_tree.selection()
        if selection:
            # Get index
            item_index = int(queue_tree.item(selection[0])['text']) - 1

            # Remove from queue
            queue_attr = f"equation_queue_{subject.lower()}"
            subject_queue = getattr(self, queue_attr, [])

            if 0 <= item_index < len(subject_queue):
                subject_queue.pop(item_index)
                self.update_subject_queue_display(subject)
                messagebox.showinfo("Removed", f"Equation removed from {subject} queue!")

    def create_file_panel(self, parent):
        """Create file import and navigation panel"""
        file_frame = ttk.LabelFrame(parent, text="Files", padding=5)
        parent.add(file_frame, weight=1)

        # Import button
        ttk.Button(file_frame, text="Import PDF/Images",
                  command=self.import_files).pack(fill=tk.X, pady=(0, 5))

        # File list
        list_frame = ttk.Frame(file_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        self.file_listbox = tk.Listbox(list_frame)
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        self.file_listbox.config(yscrollcommand=scrollbar.set)

        self.file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.file_listbox.bind('<<ListboxSelect>>', self.on_file_select)

        # Navigation
        nav_frame = ttk.Frame(file_frame)
        nav_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(nav_frame, text="◀", command=self.prev_page).pack(side=tk.LEFT)
        self.page_label = ttk.Label(nav_frame, text="Page 0/0")
        self.page_label.pack(side=tk.LEFT, expand=True)
        ttk.Button(nav_frame, text="▶", command=self.next_page).pack(side=tk.RIGHT)

    def create_preview_panel(self, parent):
        """Create preview and region selection panel"""
        preview_frame = ttk.LabelFrame(parent, text="Preview & Selection", padding=5)
        parent.add(preview_frame, weight=2)

        # Canvas for image display
        canvas_frame = ttk.Frame(preview_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        self.canvas = tk.Canvas(canvas_frame, bg='white')
        h_scroll = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        v_scroll = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        self.canvas.config(xscrollcommand=h_scroll.set, yscrollcommand=v_scroll.set)

        self.canvas.grid(row=0, column=0, sticky='nsew')
        h_scroll.grid(row=1, column=0, sticky='ew')
        v_scroll.grid(row=0, column=1, sticky='ns')

        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)

        # Bind mouse events for region selection
        self.canvas.bind('<Button-1>', self.start_selection)
        self.canvas.bind('<B1-Motion>', self.update_selection)
        self.canvas.bind('<ButtonRelease-1>', self.end_selection)

        # Bind mouse wheel for zoom
        self.canvas.bind('<MouseWheel>', self.on_mouse_wheel)
        self.canvas.bind('<Control-Button-4>', self.on_mouse_wheel)  # Linux
        self.canvas.bind('<Control-Button-5>', self.on_mouse_wheel)  # Linux

        # Zoom controls
        zoom_frame = ttk.Frame(preview_frame)
        zoom_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(zoom_frame, text="Zoom In", command=self.zoom_in).pack(side=tk.LEFT, padx=2)
        ttk.Button(zoom_frame, text="Zoom Out", command=self.zoom_out).pack(side=tk.LEFT, padx=2)
        ttk.Button(zoom_frame, text="Fit", command=self.fit_to_window).pack(side=tk.LEFT, padx=2)

    def create_editor_panel(self, parent):
        """Create equation editor panel"""
        editor_frame = ttk.LabelFrame(parent, text="Equation Editor", padding=5)
        parent.add(editor_frame, weight=1)

        # OCR result display
        ttk.Label(editor_frame, text="Recognized Text:").pack(anchor=tk.W)
        self.ocr_text = tk.Text(editor_frame, height=4, wrap=tk.WORD)
        self.ocr_text.pack(fill=tk.X, pady=(0, 5))

        # LaTeX editor
        ttk.Label(editor_frame, text="LaTeX Editor:").pack(anchor=tk.W)
        self.latex_text = tk.Text(editor_frame, height=6, wrap=tk.WORD)
        self.latex_text.pack(fill=tk.X, pady=(0, 5))

        # Preview area (placeholder for MathJax/KaTeX integration)
        ttk.Label(editor_frame, text="Preview:").pack(anchor=tk.W)
        self.preview_frame = ttk.Frame(editor_frame, relief=tk.SUNKEN, borderwidth=1)
        self.preview_frame.pack(fill=tk.X, pady=(0, 5), ipady=20)

        # OCR Status Display (LaTeX-OCR Only)
        ocr_frame = ttk.LabelFrame(editor_frame, text="OCR Status", padding=5)
        ocr_frame.pack(fill=tk.X, pady=(0, 5))

        if self.latex_ocr and self.latex_ocr.is_available():
            ttk.Label(ocr_frame, text="✅ LaTeX-OCR Ready - Mathematical OCR Engine",
                     foreground="green").pack(side=tk.LEFT)
        else:
            ttk.Label(ocr_frame, text="❌ LaTeX-OCR Not Available - Install pix2tex",
                     foreground="red").pack(side=tk.LEFT)

        # Buttons
        button_frame = ttk.Frame(editor_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="Process OCR",
                  command=self.process_selected_region).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Fix LaTeX",
                  command=self.fix_latex_manually).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Add to Queue",
                  command=self.add_to_queue).pack(side=tk.LEFT, padx=2)

    def create_queue_panel(self, parent):
        """Create equation queue panel"""
        queue_frame = ttk.LabelFrame(parent, text="Equation Queue", padding=5)
        parent.add(queue_frame, weight=1)

        # Queue list with scrollbar
        list_frame = ttk.Frame(queue_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # Create treeview for better display
        columns = ('Page', 'LaTeX', 'Confidence')
        self.queue_tree = ttk.Treeview(list_frame, columns=columns, show='tree headings', height=6)

        self.queue_tree.heading('#0', text='#')
        self.queue_tree.heading('Page', text='Page')
        self.queue_tree.heading('LaTeX', text='LaTeX')
        self.queue_tree.heading('Confidence', text='Confidence')

        self.queue_tree.column('#0', width=50)
        self.queue_tree.column('Page', width=80)
        self.queue_tree.column('LaTeX', width=300)
        self.queue_tree.column('Confidence', width=100)

        queue_scroll = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.queue_tree.yview)
        self.queue_tree.config(yscrollcommand=queue_scroll.set)

        self.queue_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        queue_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Enable text selection and copying from queue
        self.queue_tree.bind('<Double-1>', self.copy_latex_from_queue)
        self.queue_tree.bind('<Button-3>', self.show_queue_context_menu)  # Right-click menu

        # Queue controls
        queue_controls = ttk.Frame(queue_frame)
        queue_controls.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(queue_controls, text="Copy LaTeX",
                  command=self.copy_selected_latex).pack(side=tk.LEFT, padx=2)
        ttk.Button(queue_controls, text="Edit Selected",
                  command=self.edit_selected_equation).pack(side=tk.LEFT, padx=2)
        ttk.Button(queue_controls, text="Remove Selected",
                  command=self.remove_from_queue).pack(side=tk.LEFT, padx=2)
        ttk.Button(queue_controls, text="Clear All",
                  command=self.clear_queue).pack(side=tk.LEFT, padx=2)

    def import_files(self):
        """Import PDF or image files"""
        print("🔥 DEBUG: import_files called")

        filetypes = [
            ('All supported', '*.pdf;*.png;*.jpg;*.jpeg'),
            ('PDF files', '*.pdf'),
            ('Image files', '*.png;*.jpg;*.jpeg'),
            ('All files', '*.*')
        ]

        try:
            files = filedialog.askopenfilenames(
                title="Select PDF or Image files",
                filetypes=filetypes
            )

            print(f"🔥 DEBUG: Selected files: {files}")

            if files:
                self.current_files = list(files)
                self.load_files()
            else:
                print("🔥 DEBUG: No files selected")
        except Exception as e:
            print(f"🔥 DEBUG: Error in import_files: {e}")
            messagebox.showerror("Import Error", f"Failed to import files: {str(e)}")

    def load_files(self):
        """Load and process selected files"""
        print(f"🔥 DEBUG: load_files called with {len(self.current_files)} files")

        self.current_images = []

        # Clear main file listbox if it exists
        if hasattr(self, 'file_listbox'):
            self.file_listbox.delete(0, tk.END)

        for file_path in self.current_files:
            filename = os.path.basename(file_path)
            print(f"🔥 DEBUG: Processing file: {filename}")

            if file_path.lower().endswith('.pdf'):
                # Convert PDF to images
                try:
                    print(f"🔥 DEBUG: Converting PDF to images: {filename}")
                    images = convert_from_path(file_path, dpi=200)
                    print(f"🔥 DEBUG: PDF converted to {len(images)} images")

                    for i, img in enumerate(images):
                        self.current_images.append({
                            'image': img,
                            'filename': f"{filename} - Page {i+1}",
                            'source_file': file_path,
                            'page_num': i+1
                        })
                        # Add to main file listbox if it exists
                        if hasattr(self, 'file_listbox'):
                            self.file_listbox.insert(tk.END, f"{filename} - Page {i+1}")
                        print(f"🔥 DEBUG: Added page {i+1} of {filename}")
                except Exception as e:
                    print(f"🔥 DEBUG: PDF load error: {e}")
                    messagebox.showerror("Error", f"Failed to load PDF {filename}: {str(e)}")
            else:
                # Load image file
                try:
                    print(f"🔥 DEBUG: Loading image: {filename}")
                    img = Image.open(file_path)
                    print(f"🔥 DEBUG: Image loaded: {img.size}")

                    self.current_images.append({
                        'image': img,
                        'filename': filename,
                        'source_file': file_path,
                        'page_num': 1
                    })
                    # Add to main file listbox if it exists
                    if hasattr(self, 'file_listbox'):
                        self.file_listbox.insert(tk.END, filename)
                    print(f"🔥 DEBUG: Added image {filename}")
                except Exception as e:
                    print(f"🔥 DEBUG: Image load error: {e}")
                    messagebox.showerror("Error", f"Failed to load image {filename}: {str(e)}")

        print(f"🔥 DEBUG: Total images loaded: {len(self.current_images)}")

        # Also update subject-specific file lists
        subjects = ['Mathematics', 'Chemistry', 'Physics']
        for subject in subjects:
            # Copy images to subject-specific storage
            images_attr = f"current_images_{subject.lower()}"
            setattr(self, images_attr, self.current_images.copy())

            # Update subject-specific file list display
            self.update_subject_file_list(subject)

        if self.current_images:
            self.current_page = 0
            self.display_current_image()
        else:
            print("🔥 DEBUG: No images loaded successfully")

    def display_current_image(self):
        """Display the current image in the canvas with zoom support"""
        print(f"🔥 DEBUG: display_current_image called")

        if not self.current_images:
            print("🔥 DEBUG: No images to display")
            return

        print(f"🔥 DEBUG: Displaying page {self.current_page} of {len(self.current_images)}")

        img_data = self.current_images[self.current_page]
        img = img_data['image']

        print(f"🔥 DEBUG: Image size: {img.size}, mode: {img.mode}")

        # Store original image for zoom operations
        self.original_image = img

        # Apply zoom if needed
        if self.zoom_factor != 1.0:
            new_width = int(img.width * self.zoom_factor)
            new_height = int(img.height * self.zoom_factor)
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            print(f"🔥 DEBUG: Resized to: {img.size}")

        try:
            # Convert to PhotoImage for display
            self.display_image = ImageTk.PhotoImage(img)
            print(f"🔥 DEBUG: PhotoImage created successfully")

            # Get current subject and its canvas
            current_subject = self.get_current_subject()
            canvas_attr = f"canvas_{current_subject.lower()}"
            canvas = getattr(self, canvas_attr, None)

            if canvas:
                # Clear canvas and display image
                canvas.delete("all")
                canvas.create_image(0, 0, anchor=tk.NW, image=self.display_image)
                print(f"🔥 DEBUG: Image displayed on {current_subject} canvas")

                # Update scroll region
                canvas.config(scrollregion=canvas.bbox("all"))
            else:
                print(f"🔥 DEBUG: No canvas found for {current_subject}")

            # Update page label if it exists
            page_label_attr = f"page_label_{current_subject.lower()}"
            page_label = getattr(self, page_label_attr, None)
            if page_label:
                total_pages = len(self.current_images)
                page_label.config(text=f"Page {self.current_page + 1}/{total_pages} (Zoom: {self.zoom_factor:.1f}x)")
                print(f"🔥 DEBUG: Page label updated for {current_subject}")

        except Exception as e:
            print(f"🔥 DEBUG: Error displaying image: {e}")
            messagebox.showerror("Display Error", f"Failed to display image: {str(e)}")

    def start_selection(self, event):
        """Start region selection - DEPRECATED: Use subject-specific selection methods"""
        # Get current subject and its canvas
        current_subject = self.get_current_subject()
        canvas_attr = f"canvas_{current_subject.lower()}"
        canvas = getattr(self, canvas_attr, None)

        if canvas:
            self.selection_start = (canvas.canvasx(event.x), canvas.canvasy(event.y))

    def update_selection(self, event):
        """Update selection rectangle - DEPRECATED: Use subject-specific selection methods"""
        # Get current subject and its canvas
        current_subject = self.get_current_subject()
        canvas_attr = f"canvas_{current_subject.lower()}"
        canvas = getattr(self, canvas_attr, None)

        if canvas and self.selection_start:
            current_pos = (canvas.canvasx(event.x), canvas.canvasy(event.y))

            # Remove previous rectangle
            if self.selection_rect:
                canvas.delete(self.selection_rect)

            # Draw new rectangle
            self.selection_rect = canvas.create_rectangle(
                self.selection_start[0], self.selection_start[1],
                current_pos[0], current_pos[1],
                outline='red', width=2
            )

    def end_selection(self, event):
        """End region selection - DEPRECATED: Use subject-specific selection methods"""
        # Get current subject and its canvas
        current_subject = self.get_current_subject()
        canvas_attr = f"canvas_{current_subject.lower()}"
        canvas = getattr(self, canvas_attr, None)

        if canvas and self.selection_start:
            current_pos = (canvas.canvasx(event.x), canvas.canvasy(event.y))

            # Calculate selection bounds
            x1, y1 = self.selection_start
            x2, y2 = current_pos

            # Ensure proper ordering
            x, y = min(x1, x2), min(y1, y2)
            width, height = abs(x2 - x1), abs(y2 - y1)

            if width > 10 and height > 10:  # Minimum selection size
                # Store selection for OCR processing
                self.current_selection = {
                    'x': int(x), 'y': int(y),
                    'width': int(width), 'height': int(height)
                }

    def process_selected_region(self):
        """Process the selected region with OCR for the current subject"""
        print("🔥 DEBUG: process_selected_region called")
        import sys
        sys.stdout.flush()

        # Get current subject
        current_subject = self.get_current_subject()

        # Get subject-specific data
        selection_attr = f"current_selection_{current_subject.lower()}"
        images_attr = f"current_images_{current_subject.lower()}"
        page_attr = f"current_page_{current_subject.lower()}"

        current_selection = getattr(self, selection_attr, None)
        current_images = getattr(self, images_attr, [])
        current_page = getattr(self, page_attr, 0)

        if not current_selection or not current_images:
            print(f"🔥 DEBUG: No selection or images for {current_subject}")
            sys.stdout.flush()
            messagebox.showwarning("Warning", f"Please select a region in {current_subject} first.")
            return

        # Get current image and selection
        if current_page >= len(current_images):
            messagebox.showwarning("Warning", f"No image available for {current_subject}")
            return

        img_data = current_images[current_page]
        img = img_data['image']
        sel = current_selection

        # Adjust selection coordinates for zoom factor
        zoom_attr = f"zoom_factor_{current_subject.lower()}"
        zoom_factor = getattr(self, zoom_attr, 1.0)

        actual_x = int(sel['x'] / zoom_factor)
        actual_y = int(sel['y'] / zoom_factor)
        actual_width = int(sel['width'] / zoom_factor)
        actual_height = int(sel['height'] / zoom_factor)

        # Ensure coordinates are within image bounds
        actual_x = max(0, min(actual_x, img.width - 1))
        actual_y = max(0, min(actual_y, img.height - 1))
        actual_width = min(actual_width, img.width - actual_x)
        actual_height = min(actual_height, img.height - actual_y)

        # Crop the selected region from original image
        cropped = img.crop((actual_x, actual_y,
                           actual_x + actual_width,
                           actual_y + actual_height))

        # Get current subject and update appropriate widgets
        current_subject = self.get_current_subject()
        widgets = self.get_subject_widgets(current_subject)

        # Show a message that OCR is processing
        if widgets['ocr_text']:
            widgets['ocr_text'].delete(1.0, tk.END)
            widgets['ocr_text'].insert(1.0, f"Processing {current_subject} OCR...")
        if widgets['latex_text']:
            widgets['latex_text'].delete(1.0, tk.END)
            widgets['latex_text'].insert(1.0, "Processing...")

        # Also update the old widgets for backward compatibility
        if hasattr(self, 'ocr_text'):
            self.ocr_text.delete(1.0, tk.END)
            self.ocr_text.insert(1.0, f"Processing {current_subject} OCR...")
        if hasattr(self, 'latex_text'):
            self.latex_text.delete(1.0, tk.END)
            self.latex_text.insert(1.0, "Processing...")

        # Preprocess for better OCR
        processed_img = self.preprocess_for_ocr(cropped)

        # Save debug images with detailed info
        try:
            import tempfile
            temp_dir = tempfile.gettempdir()

            # Save original cropped region
            cropped_path = os.path.join(temp_dir, 'debug_cropped_region.png')
            cropped.save(cropped_path)

            # Save processed image
            processed_path = os.path.join(temp_dir, 'debug_processed.png')
            cv2.imwrite(processed_path, processed_img)

            # Save original image with selection box
            original_img_data = current_images[current_page]
            original_img = original_img_data['image']  # Get the PIL Image from the dict
            original_with_box = original_img.copy()
            from PIL import ImageDraw
            draw = ImageDraw.Draw(original_with_box)
            draw.rectangle([actual_x, actual_y, actual_x + actual_width, actual_y + actual_height],
                          outline='red', width=3)
            original_with_box_path = os.path.join(temp_dir, 'debug_original_with_selection.png')
            original_with_box.save(original_with_box_path)

            print(f"🔍 DEBUG: Selection coordinates: x={actual_x}, y={actual_y}, w={actual_width}, h={actual_height}")
            print(f"🔍 DEBUG: Original image size: {original_img.size}")
            print(f"🔍 DEBUG: Cropped region size: {cropped.size}")
            print(f"🔍 DEBUG: Debug images saved to:")
            print(f"  - Original with selection: {original_with_box_path}")
            print(f"  - Cropped region: {cropped_path}")
            print(f"  - Processed for OCR: {processed_path}")

        except Exception as e:
            print(f"Failed to save debug images: {e}")

        # Run OCR in a separate thread (LaTeX-OCR only)
        print(f"🔥 DEBUG: Using LaTeX-OCR (only method available)")
        print(f"🔥 DEBUG: LaTeX-OCR available: {self.latex_ocr and self.latex_ocr.is_available()}")
        print(f"🔥 DEBUG: Starting OCR processing thread...")

        # Force flush to ensure debug output appears
        import sys
        sys.stdout.flush()

        threading.Thread(target=self.run_ocr_with_method,
                        args=(cropped, processed_img, 'latex_ocr'), daemon=True).start()

    def preprocess_for_ocr(self, img):
        """Preprocess image for better OCR results with mathematical content optimization"""
        # Convert PIL to OpenCV format
        cv_img = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

        # Convert to grayscale
        gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)

        # Resize image for better OCR (scale up small text)
        height, width = gray.shape
        if height < 100 or width < 100:
            scale_factor = max(2, 200 // min(height, width))
            gray = cv2.resize(gray, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_CUBIC)

        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)

        # Apply adaptive thresholding for better contrast
        thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)

        # Morphological operations to clean up the image
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

        # Invert if background is dark (common in some PDFs)
        if np.mean(cleaned) < 127:
            cleaned = cv2.bitwise_not(cleaned)

        # Apply slight dilation to make text thicker (helps with OCR)
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 1))
        final = cv2.dilate(cleaned, kernel, iterations=1)

        return final

    def run_ocr_with_method(self, original_img, processed_img, method):
        """Run OCR processing with optional AI - checks AI settings for faster response"""
        try:
            # Check AI settings for Vision AI
            ai_enabled = self.settings.get('ai_enabled', False)
            vision_ai_enabled = self.settings.get('ai_vision_enabled', False)

            # Priority 1: Try Qwen-VL Vision AI only if enabled
            if ai_enabled and vision_ai_enabled and self.qwen_vl and self.qwen_vl.is_available():
                print(f"👁️🔥 DEBUG: Processing with Qwen-VL Vision AI (AI enabled)...")
                result = self.qwen_vl.process_image(original_img, self.get_current_subject())

                if result and result.get('latex'):
                    latex_text = result['latex']
                    confidence = result.get('confidence', 95.0)
                    print(f"👁️🔥 DEBUG: Qwen-VL result: {latex_text} (confidence: {confidence}%)")

                    # Update UI with Qwen-VL result
                    self.root.after(0, self.update_latex_ocr_result, latex_text, confidence, 'Qwen-VL')
                    return
                else:
                    print("⚠️ Qwen-VL failed, falling back to LaTeX-OCR...")
            elif not ai_enabled:
                print("🚀 AI disabled - skipping Vision AI for faster processing")
            elif not vision_ai_enabled:
                print("🚀 Vision AI disabled - skipping for faster processing")

            # Priority 2: LaTeX-OCR (always available as fallback)
            if self.latex_ocr and self.latex_ocr.is_available():
                print(f"🔥 DEBUG: Processing with LaTeX-OCR...")
                result = self.latex_ocr.process_image(original_img)
                latex_text = result['latex']
                confidence = result['confidence']
                print(f"🔥 DEBUG: LaTeX-OCR result: {latex_text} (confidence: {confidence}%)")

                # Update UI with LaTeX-OCR result
                self.root.after(0, self.update_latex_ocr_result, latex_text, confidence, 'LaTeX-OCR')
            else:
                # No OCR methods available
                error_msg = "No OCR methods are available. Please check the installation."
                self.root.after(0, lambda: messagebox.showerror("OCR Error", error_msg))

        except Exception as e:
            error_msg = f"OCR processing error: {str(e)}"
            print(f"🔥 DEBUG: OCR error: {error_msg}")
            self.root.after(0, lambda: messagebox.showerror("OCR Error", error_msg))





    def update_latex_ocr_result(self, latex_text, confidence, method_name):
        """Update UI with LaTeX-OCR result (already in LaTeX format)"""
        print(f"🔥 DEBUG: update_latex_ocr_result called!")
        print(f"🔥 DEBUG: Method: {method_name}")
        print(f"🔥 DEBUG: Input LaTeX: {latex_text}")
        print(f"🔥 DEBUG: Confidence: {confidence}")

        # Get current subject
        current_subject = self.get_current_subject()
        print(f"🔥 DEBUG: Current subject: {current_subject}")

        # Get subject-specific widgets
        widgets = self.get_subject_widgets(current_subject)

        # Update subject-specific OCR text widget
        if widgets['ocr_text']:
            widgets['ocr_text'].delete(1.0, tk.END)
            widgets['ocr_text'].insert(1.0, f"[{method_name}] {current_subject} LaTeX output")

        # Check AI settings for processing
        ai_enabled = self.settings.get('ai_enabled', False)
        text_enhancement_enabled = self.settings.get('ai_text_enhancement_enabled', False)
        subject_processing_enabled = self.settings.get('ai_subject_processing_enabled', False)

        # AI-POWERED: Apply intelligent subject-specific processing (if enabled)
        if ai_enabled and subject_processing_enabled:
            print(f"🤖 DEBUG: Applying AI-powered {current_subject} processing...")
            subject_processed_latex = self.apply_subject_specific_processing(latex_text, current_subject)
        else:
            print(f"🚀 Subject-specific AI disabled - using basic rule-based processing for faster response")
            subject_processed_latex = self.apply_basic_subject_processing(latex_text, current_subject)

        # QWEN2.5-1.5B: Intelligent LaTeX enhancement (if enabled)
        if ai_enabled and text_enhancement_enabled and self.qwen25_text and self.qwen25_text.is_available():
            print(f"🧠 DEBUG: Enhancing LaTeX with Qwen2.5-1.5B...")
            enhancement_result = self.qwen25_text.enhance_latex(subject_processed_latex, current_subject)
            enhanced_latex = enhancement_result['latex']
            enhancement_confidence = enhancement_result['confidence']
            improvements = enhancement_result['improvements']

            print(f"🧠✅ Qwen2.5-1.5B enhancement complete!")
            print(f"   Enhanced LaTeX: {enhanced_latex[:100]}...")
            print(f"   Enhancement confidence: {enhancement_confidence}%")
            print(f"   Improvements: {', '.join(improvements)}")

            # Use enhanced LaTeX and update confidence
            subject_processed_latex = enhanced_latex
            confidence = max(confidence, enhancement_confidence)
        else:
            if not ai_enabled or not text_enhancement_enabled:
                print(f"🚀 Text enhancement AI disabled - skipping for faster processing")
            else:
                print(f"⚠️ Qwen2.5-1.5B not available - using basic processing")

        # Universal post-processing for final perfection (always applied for quality)
        print(f"🔧 DEBUG: Applying universal postprocessor...")
        perfected_latex = self.apply_basic_postprocessing(subject_processed_latex, current_subject)
        print(f"🔧 DEBUG: Post-processing complete!")

        # Update subject-specific LaTeX text widget
        if widgets['latex_text']:
            widgets['latex_text'].delete(1.0, tk.END)
            widgets['latex_text'].insert(1.0, perfected_latex)

        # Also update the original widgets for backward compatibility
        if hasattr(self, 'ocr_text'):
            self.ocr_text.delete(1.0, tk.END)
            self.ocr_text.insert(1.0, f"[{method_name}] Direct LaTeX output")

        if hasattr(self, 'latex_text'):
            self.latex_text.delete(1.0, tk.END)
            self.latex_text.insert(1.0, perfected_latex)

        # Store confidence for later use
        self.current_confidence = confidence

        print(f"✅ {method_name} completed with {confidence:.1f}% confidence for {current_subject}")
        print(f"Original LaTeX: {latex_text}")
        print(f"Perfected LaTeX: {perfected_latex}")

    def apply_subject_specific_processing(self, latex_text, subject):
        """AI-POWERED: Apply intelligent subject-specific LaTeX processing"""
        print(f"🤖 Applying AI-powered {subject} processing...")

        # Use AI for all subjects with domain-specific intelligence
        if subject == "Chemistry":
            processed_latex = self.ai_chemistry_processor(latex_text)
        elif subject == "Physics":
            processed_latex = self.ai_physics_processor(latex_text)
        elif subject == "Mathematics":
            processed_latex = self.ai_mathematics_processor(latex_text)
        else:
            # Fallback to general AI processing
            processed_latex = self.ai_general_processor(latex_text, subject)

        return processed_latex

    def apply_basic_subject_processing(self, latex_text, subject):
        """Basic rule-based subject-specific processing (faster, no AI)"""
        print(f"🔧 Applying basic {subject} processing for faster response...")

        # Use basic rule-based processing for each subject
        if subject == "Chemistry":
            processed_latex = self.apply_chemistry_latex_fixes(latex_text)
        elif subject == "Physics":
            processed_latex = self.apply_physics_fixes(latex_text)
        elif subject == "Mathematics":
            processed_latex = self.apply_mathematics_fixes(latex_text)
        else:
            # Fallback to general processing
            processed_latex = self.comprehensive_latex_fix(latex_text)

        return processed_latex

    def apply_basic_postprocessing(self, latex_text, subject):
        """Basic universal postprocessing (faster, no AI)"""
        print(f"🔧 Applying basic postprocessing for {subject}...")

        # Step 1: Apply Word compatibility fixes
        latex_text = self.fix_word_compatibility(latex_text)

        # Step 2: Apply basic LaTeX cleanup
        final_latex = self.perfect_latex_postprocess(latex_text)

        return final_latex

    # ==================== AI-POWERED SUBJECT PROCESSORS ====================

    def ai_chemistry_processor(self, latex_text):
        """AI-POWERED chemistry LaTeX processor - 100% perfect LaTeX output only"""
        print(f"🧪🤖 AI Chemistry Processor: {latex_text}")

        try:
            # Try Qwen2.5-1.5B for perfect chemistry LaTeX first
            if hasattr(self, 'qwen25_text') and self.qwen25_text and self.qwen25_text.is_available():
                print("🧪🤖 Using Qwen2.5-1.5B for perfect chemistry LaTeX...")
                result = self.qwen25_text.get_chemistry_latex_only(latex_text)
                return result

            # Fallback to AI Math Processor for LaTeX-only output
            else:
                print("🧪🔄 Using AI Math Processor for perfect chemistry LaTeX...")
                from ai_math_processor import AIMathProcessor

                # Initialize AI processor
                ai_processor = AIMathProcessor()

                # Get LaTeX-only output for chemistry
                perfect_latex = ai_processor.get_chemistry_latex_only(latex_text)

                return perfect_latex

        except Exception as e:
            print(f"🧪❌ AI Chemistry processing failed: {e}")
            print("🧪🔄 Falling back to basic chemistry processing...")
            # Fallback to basic rule-based processing
            return self.apply_chemistry_latex_fixes(latex_text)



    def ai_physics_processor(self, latex_text):
        """Professional physics equation processing using rule-based methods"""
        print(f"⚛️🔧 Professional Physics Processor analyzing: {latex_text}")

        # Use professional rule-based physics processing
        return self.apply_physics_fixes(latex_text)

    def ai_mathematics_processor(self, latex_text):
        """Professional mathematics equation processing using rule-based methods"""
        print(f"📐🔧 Professional Mathematics Processor analyzing: {latex_text}")

        # Use professional rule-based mathematics processing
        return self.apply_mathematics_fixes(latex_text)

    def ai_general_processor(self, latex_text, subject):
        """Professional general processing for any subject using rule-based methods"""
        print(f"🔧 Professional General Processor analyzing {subject}: {latex_text}")

        # Use professional rule-based processing
        return self.comprehensive_latex_fix(latex_text)

    def ai_universal_postprocessor(self, latex_text, subject):
        """Professional universal postprocessing for final perfection using rule-based methods"""
        print(f"🔧 Professional Universal Postprocessor for {subject}: {latex_text}")

        # Step 1: Apply Word compatibility fixes FIRST
        latex_text = self.fix_word_compatibility(latex_text)
        print(f"🔧 After Word compatibility: {latex_text}")

        # Step 2: Use professional rule-based postprocessing
        final_latex = self.perfect_latex_postprocess(latex_text)
        print(f"🔧 Final processed LaTeX: {final_latex}")

        return final_latex

    def apply_chemistry_latex_fixes(self, latex_text):
        """Apply PROFESSIONAL chemistry-specific LaTeX fixes - 100% perfect output"""
        import re

        print(f"🧪 Starting professional chemistry LaTeX processing: {latex_text}")

        # Step 0: Fix common OCR errors in chemistry first
        latex_text = self.fix_chemistry_ocr_errors(latex_text)

        # Clean up any existing LaTeX commands first
        latex_text = self.clean_latex_for_chemistry(latex_text)

        # Step 1: Professional chemical equation arrows (comprehensive and order-sensitive)
        # Handle equilibrium arrows first (more specific patterns)
        latex_text = re.sub(r'<-->', r' \\leftrightarrow ', latex_text)
        latex_text = re.sub(r'<->', r' \\leftrightarrow ', latex_text)
        latex_text = re.sub(r'⇌', r' \\leftrightarrow ', latex_text)

        # Handle single direction arrows
        latex_text = re.sub(r'-->', r' \\rightarrow ', latex_text)
        latex_text = re.sub(r'->', r' \\rightarrow ', latex_text)
        latex_text = re.sub(r'<--', r' \\leftarrow ', latex_text)
        latex_text = re.sub(r'<-', r' \\leftarrow ', latex_text)

        # Standardize existing LaTeX arrows
        latex_text = re.sub(r'\\to\\b', r' \\rightarrow ', latex_text)  # Replace \to with \rightarrow
        latex_text = re.sub(r'\\longrightarrow', r' \\rightarrow ', latex_text)  # Standardize arrows

        # Step 2: Complete periodic table elements (all 118 elements)
        elements = [
            # Period 1
            'H', 'He',
            # Period 2
            'Li', 'Be', 'B', 'C', 'N', 'O', 'F', 'Ne',
            # Period 3
            'Na', 'Mg', 'Al', 'Si', 'P', 'S', 'Cl', 'Ar',
            # Period 4
            'K', 'Ca', 'Sc', 'Ti', 'V', 'Cr', 'Mn', 'Fe', 'Co', 'Ni', 'Cu', 'Zn', 'Ga', 'Ge', 'As', 'Se', 'Br', 'Kr',
            # Period 5
            'Rb', 'Sr', 'Y', 'Zr', 'Nb', 'Mo', 'Tc', 'Ru', 'Rh', 'Pd', 'Ag', 'Cd', 'In', 'Sn', 'Sb', 'Te', 'I', 'Xe',
            # Period 6
            'Cs', 'Ba', 'La', 'Ce', 'Pr', 'Nd', 'Pm', 'Sm', 'Eu', 'Gd', 'Tb', 'Dy', 'Ho', 'Er', 'Tm', 'Yb', 'Lu',
            'Hf', 'Ta', 'W', 'Re', 'Os', 'Ir', 'Pt', 'Au', 'Hg', 'Tl', 'Pb', 'Bi', 'Po', 'At', 'Rn',
            # Period 7
            'Fr', 'Ra', 'Ac', 'Th', 'Pa', 'U', 'Np', 'Pu', 'Am', 'Cm', 'Bk', 'Cf', 'Es', 'Fm', 'Md', 'No', 'Lr',
            'Rf', 'Db', 'Sg', 'Bh', 'Hs', 'Mt', 'Ds', 'Rg', 'Cn', 'Nh', 'Fl', 'Mc', 'Lv', 'Ts', 'Og'
        ]

        # Step 3: Professional element formatting - ensure ALL elements are in \mathrm{}
        for element in elements:
            # Replace standalone elements with \mathrm{} format (avoid double wrapping)
            pattern = rf'(?<!\\mathrm\{{)\\b{re.escape(element)}\\b(?!\}})'
            replacement = rf'\\mathrm{{{element}}}'
            latex_text = re.sub(pattern, replacement, latex_text)

        # Step 4: Professional chemical formulas with subscripts
        # Handle already formatted elements with subscripts
        latex_text = re.sub(r'\\mathrm\{([A-Z][a-z]?)\}(\d+)', r'\\mathrm{\1}_{\\mathrm{\2}}', latex_text)
        # Handle unformatted elements with subscripts (more comprehensive)
        latex_text = re.sub(r'([A-Z][a-z]?)(\d+)(?!\\}|_)', r'\\mathrm{\1}_{\\mathrm{\2}}', latex_text)

        # Step 4b: Fix any remaining unformatted elements
        for element in elements:
            # Catch any elements that weren't formatted in previous steps
            pattern = rf'(?<!\\mathrm\{{){re.escape(element)}(?!\}})'
            replacement = rf'\\mathrm{{{element}}}'
            latex_text = re.sub(pattern, replacement, latex_text)

        # Step 5: Professional chemical states with proper formatting
        latex_text = re.sub(r'\(s\)(?!\\})', r'_{\\mathrm{(s)}}', latex_text)
        latex_text = re.sub(r'\(l\)(?!\\})', r'_{\\mathrm{(l)}}', latex_text)
        latex_text = re.sub(r'\(g\)(?!\\})', r'_{\\mathrm{(g)}}', latex_text)
        latex_text = re.sub(r'\(aq\)(?!\\})', r'_{\\mathrm{(aq)}}', latex_text)

        # Step 6: Professional heat and energy terms
        latex_text = re.sub(r'\\Delta|Delta(?!\\)', r'\\Delta', latex_text)
        latex_text = re.sub(r'\\+\\s*heat\\b', r' + \\Delta H', latex_text)
        latex_text = re.sub(r'\\+\\s*energy\\b', r' + \\Delta H', latex_text)

        # Step 7: Professional spacing around operators (critical for quality)
        latex_text = re.sub(r'\\s*\\+\\s*', r' + ', latex_text)
        latex_text = re.sub(r'\\s*\\rightarrow\\s*', r' \\rightarrow ', latex_text)
        latex_text = re.sub(r'\\s*\\leftrightarrow\\s*', r' \\leftrightarrow ', latex_text)

        # Step 8: Professional charges (superscripts) - enhanced pattern
        # Handle charges at end of formulas or before spaces/arrows
        # First handle charges with numbers (like 2+, 3-)
        latex_text = re.sub(r'(\\mathrm\{[^}]+\}|[A-Za-z])(\d+)([+-]+)(?=\\s|$|\\s*\\rightarrow|\\s*\\leftrightarrow)',
                           r'\1^{\\mathrm{\2\3}}', latex_text)
        # Then handle simple charges (like +, -)
        latex_text = re.sub(r'(\\mathrm\{[^}]+\}|[A-Za-z])([+-]+)(?=\\s|$|\\s*\\rightarrow|\\s*\\leftrightarrow)',
                           r'\1^{\\mathrm{\2}}', latex_text)

        # Step 9: Professional coefficient handling
        # Ensure coefficients are properly spaced from formulas
        latex_text = re.sub(r'(\\d+)\\s*(\\mathrm\{)', r'\1\\,\2', latex_text)

        # Step 10: Final professional cleanup
        latex_text = re.sub(r'\\s+', ' ', latex_text)  # Normalize spaces
        latex_text = latex_text.strip()

        print(f"🧪✅ Professional Chemistry LaTeX completed: {latex_text}")
        return latex_text

    def validate_chemistry_latex(self, latex_text):
        """Validate and enhance chemistry LaTeX for professional quality"""
        import re

        print(f"🧪🔍 Validating chemistry LaTeX: {latex_text}")

        # Validation Step 1: Ensure all elements are properly formatted
        # Check for unformatted elements and fix them
        elements_pattern = r'(?<!\\mathrm\{)\\b([A-Z][a-z]?)\\b(?!\})'
        matches = re.findall(elements_pattern, latex_text)
        for element in matches:
            # Only format if it's a valid chemical element
            if element in ['H', 'He', 'Li', 'Be', 'B', 'C', 'N', 'O', 'F', 'Ne', 'Na', 'Mg', 'Al', 'Si', 'P', 'S', 'Cl', 'Ar',
                          'K', 'Ca', 'Sc', 'Ti', 'V', 'Cr', 'Mn', 'Fe', 'Co', 'Ni', 'Cu', 'Zn', 'Ga', 'Ge', 'As', 'Se', 'Br', 'Kr',
                          'Rb', 'Sr', 'Y', 'Zr', 'Nb', 'Mo', 'Tc', 'Ru', 'Rh', 'Pd', 'Ag', 'Cd', 'In', 'Sn', 'Sb', 'Te', 'I', 'Xe']:
                latex_text = re.sub(rf'\\b{re.escape(element)}\\b(?!\\}})', rf'\\mathrm{{{element}}}', latex_text)

        # Validation Step 2: Fix spacing issues
        latex_text = re.sub(r'\\s*\\+\\s*', r' + ', latex_text)
        latex_text = re.sub(r'\\s*\\rightarrow\\s*', r' \\rightarrow ', latex_text)
        latex_text = re.sub(r'\\s*\\leftrightarrow\\s*', r' \\leftrightarrow ', latex_text)

        # Validation Step 3: Ensure proper subscript formatting
        latex_text = re.sub(r'\\mathrm\{([A-Z][a-z]?)\}(\d+)(?!\\})', r'\\mathrm{\1}_{\\mathrm{\2}}', latex_text)

        # Validation Step 4: Ensure proper charge formatting
        # Handle charges with numbers first
        latex_text = re.sub(r'(\\mathrm\{[^}]+\}|[A-Za-z])(\d+)([+-]+)(?=\\s|$|\\s*\\rightarrow|\\s*\\leftrightarrow)', r'\1^{\\mathrm{\2\3}}', latex_text)
        # Handle simple charges
        latex_text = re.sub(r'(\\mathrm\{[^}]+\}|[A-Za-z])([+-]+)(?=\\s|$|\\s*\\rightarrow|\\s*\\leftrightarrow)', r'\1^{\\mathrm{\2}}', latex_text)

        # Validation Step 5: Clean up multiple spaces
        latex_text = re.sub(r'\\s+', ' ', latex_text).strip()

        print(f"🧪✅ Chemistry LaTeX validation completed: {latex_text}")
        return latex_text

    def fix_chemistry_ocr_errors(self, latex_text):
        """Fix common OCR errors specific to chemistry equations"""
        import re

        print(f"🧪🔧 Fixing chemistry OCR errors: {latex_text}")

        # Critical chemistry OCR error corrections
        ocr_fixes = [
            # Element symbol corrections
            (r'\\mathrm\{Gr\}', r'\\mathrm{Cr}'),  # Chromium
            (r'\\mathrm\{Sl\}', r'\\mathrm{H}'),   # Hydrogen
            (r'\\mathrm\{G\}', r'\\mathrm{C}'),    # Carbon
            (r'\\mathrm\{T\}', r'\\mathrm{H}'),    # Hydrogen (alternative)
            (r'\\mathrm\{GO\}', r'\\mathrm{CO}'),  # Carbon monoxide
            (r'\\mathrm\{l\}', r'\\mathrm{I}'),    # Iodine

            # Without mathrm wrappers
            (r'\\bGr\\b', r'Cr'),  # Chromium
            (r'\\bSl\\b', r'H'),   # Hydrogen
            (r'\\bGO\\b', r'CO'),  # Carbon monoxide
            (r'\\bT(?=_)', r'H'),  # Hydrogen before subscript

            # With coefficients
            (r'(\\d+)Gr\\b', r'\\1Cr'),  # Coefficient + Chromium
            (r'(\\d+)Sl\\b', r'\\1H'),   # Coefficient + Hydrogen

            # In mathrm with coefficients
            (r'\\mathrm\{(\\d+)Gr\}', r'\\mathrm{\\1Cr}'),  # Coefficient + Chromium in mathrm
            (r'\\mathrm\{(\\d+)Sl\}', r'\\mathrm{\\1H}'),   # Coefficient + Hydrogen in mathrm

            # Number corrections
            (r'Θ', r'6'),          # Theta to 6
            (r'\\Theta', r'6'),    # Theta command to 6
            (r'(?<=\\mathrm\\{)l(?=\\})', r'1'),  # l to 1 in mathrm

            # Arrow corrections
            (r'\\longrightarrow', r'\\rightarrow'),  # Standardize arrows
        ]

        # Apply all OCR fixes
        for pattern, replacement in ocr_fixes:
            latex_text = re.sub(pattern, replacement, latex_text)

        print(f"🧪✅ Chemistry OCR errors fixed: {latex_text}")
        return latex_text

    def apply_chemistry_standard_format(self, latex_text):
        """Standard LaTeX chemistry format with mhchem"""
        import re

        # Clean up any existing LaTeX commands first
        latex_text = self.clean_latex_for_chemistry(latex_text)

        # Chemical equation arrows
        latex_text = re.sub(r'-->', r' \\rightarrow ', latex_text)
        latex_text = re.sub(r'<-->', r' \\leftrightarrow ', latex_text)
        latex_text = re.sub(r'<--', r' \\leftarrow ', latex_text)
        latex_text = re.sub(r'->', r' \\rightarrow ', latex_text)
        latex_text = re.sub(r'<->', r' \\leftrightarrow ', latex_text)

        # Chemical formulas (subscripts) - more precise pattern
        latex_text = re.sub(r'([A-Z][a-z]?)(\d+)', r'\\mathrm{\1}_{\\mathrm{\2}}', latex_text)

        # Chemical states with proper formatting
        latex_text = re.sub(r'\(s\)', r'_{\\mathrm{(s)}}', latex_text)
        latex_text = re.sub(r'\(l\)', r'_{\\mathrm{(l)}}', latex_text)
        latex_text = re.sub(r'\(g\)', r'_{\\mathrm{(g)}}', latex_text)
        latex_text = re.sub(r'\(aq\)', r'_{\\mathrm{(aq)}}', latex_text)

        # Heat and energy terms
        latex_text = re.sub(r'\\Delta|Delta', r'\\Delta', latex_text)
        latex_text = re.sub(r'\+\\s*heat', r' + \\Delta', latex_text)

        # Wrap in math environment
        latex_text = f"\\[ {latex_text.strip()} \\]"

        print(f"🧪 Standard Chemistry LaTeX applied: {latex_text}")
        return latex_text

    def clean_latex_for_chemistry(self, latex_text):
        """Clean LaTeX text for chemistry processing"""
        import re

        # Remove existing math environments
        latex_text = re.sub(r'\\[\[\]]', '', latex_text)
        latex_text = re.sub(r'\$+', '', latex_text)

        # Remove extra whitespace
        latex_text = re.sub(r'\s+', ' ', latex_text)

        return latex_text.strip()

    def apply_chemistry_mathjax_format(self, latex_text):
        """MathJax-compatible chemistry format"""
        import re

        # Clean up first
        latex_text = self.clean_latex_for_chemistry(latex_text)

        # MathJax chemistry formatting with proper spacing
        latex_text = re.sub(r'-->', r' \\longrightarrow ', latex_text)
        latex_text = re.sub(r'<-->', r' \\rightleftharpoons ', latex_text)
        latex_text = re.sub(r'<--', r' \\longleftarrow ', latex_text)
        latex_text = re.sub(r'->', r' \\longrightarrow ', latex_text)
        latex_text = re.sub(r'<->', r' \\rightleftharpoons ', latex_text)

        # Chemical formulas with proper MathJax subscripts
        latex_text = re.sub(r'([A-Z][a-z]?)(\d+)', r'\\mathrm{\1}_{\\mathrm{\2}}', latex_text)

        # Ensure all chemical elements are in roman font
        elements = ['H', 'He', 'Li', 'Be', 'B', 'C', 'N', 'O', 'F', 'Ne', 'Na', 'Mg', 'Al', 'Si', 'P', 'S', 'Cl', 'Ar', 'K', 'Ca', 'Cr', 'Mn', 'Fe', 'Co', 'Ni', 'Cu', 'Zn']
        for element in elements:
            # Only replace standalone elements not already in \mathrm{}
            latex_text = re.sub(rf'(?<!\\mathrm\{{)\\b{element}\\b(?!\}})', rf'\\mathrm{{{element}}}', latex_text)

        # States in roman font
        latex_text = re.sub(r'\(s\)', r'_{\\mathrm{(s)}}', latex_text)
        latex_text = re.sub(r'\(l\)', r'_{\\mathrm{(l)}}', latex_text)
        latex_text = re.sub(r'\(g\)', r'_{\\mathrm{(g)}}', latex_text)
        latex_text = re.sub(r'\(aq\)', r'_{\\mathrm{(aq)}}', latex_text)

        # Heat and energy terms
        latex_text = re.sub(r'\\Delta|Delta', r'\\Delta', latex_text)
        latex_text = re.sub(r'\+\\s*heat', r' + \\Delta', latex_text)

        # Clean up extra spaces
        latex_text = re.sub(r'\s+', ' ', latex_text)

        print(f"🧪 MathJax Chemistry format applied: {latex_text}")
        return latex_text

    def apply_chemistry_plain_latex_format(self, latex_text):
        """Plain LaTeX without mhchem package"""
        import re

        # Clean up first
        latex_text = self.clean_latex_for_chemistry(latex_text)

        # Simple arrows without special packages
        latex_text = re.sub(r'-->', r' \\to ', latex_text)
        latex_text = re.sub(r'<-->', r' \\leftrightarrow ', latex_text)
        latex_text = re.sub(r'<--', r' \\leftarrow ', latex_text)
        latex_text = re.sub(r'->', r' \\to ', latex_text)
        latex_text = re.sub(r'<->', r' \\leftrightarrow ', latex_text)

        # Chemical formulas with basic subscripts
        latex_text = re.sub(r'([A-Z][a-z]?)(\d+)', r'\1_{\2}', latex_text)

        # States as simple subscripts
        latex_text = re.sub(r'\(s\)', r'_{(s)}', latex_text)
        latex_text = re.sub(r'\(l\)', r'_{(l)}', latex_text)
        latex_text = re.sub(r'\(g\)', r'_{(g)}', latex_text)
        latex_text = re.sub(r'\(aq\)', r'_{(aq)}', latex_text)

        # Basic symbols
        latex_text = re.sub(r'\\Delta|Delta', r'\\Delta', latex_text)
        latex_text = re.sub(r'\+\\s*heat', r' + \\Delta', latex_text)

        # Clean up extra spaces
        latex_text = re.sub(r'\s+', ' ', latex_text)

        print(f"🧪 Plain LaTeX Chemistry format applied: {latex_text}")
        return latex_text

    def apply_chemistry_text_notation_format(self, latex_text):
        """Text-based chemistry notation"""
        import re

        # Clean up first
        latex_text = self.clean_latex_for_chemistry(latex_text)

        # Convert to text-based arrows
        latex_text = re.sub(r'-->', r' -> ', latex_text)
        latex_text = re.sub(r'<-->', r' <-> ', latex_text)
        latex_text = re.sub(r'<--', r' <- ', latex_text)
        latex_text = re.sub(r'->', r' -> ', latex_text)
        latex_text = re.sub(r'<->', r' <-> ', latex_text)

        # Chemical formulas - keep numbers as regular text
        latex_text = re.sub(r'([A-Z][a-z]?)(\d+)', r'\1\2', latex_text)

        # States as text
        latex_text = re.sub(r'\(s\)', r'(s)', latex_text)
        latex_text = re.sub(r'\(l\)', r'(l)', latex_text)
        latex_text = re.sub(r'\(g\)', r'(g)', latex_text)
        latex_text = re.sub(r'\(aq\)', r'(aq)', latex_text)

        # Convert heat/energy terms
        latex_text = re.sub(r'\\Delta|Delta', r'heat', latex_text)
        latex_text = re.sub(r'\+\\s*heat', r' + heat', latex_text)

        # Remove any remaining LaTeX commands
        latex_text = re.sub(r'\\[a-zA-Z]+\*?', '', latex_text)
        latex_text = re.sub(r'[{}]', '', latex_text)
        latex_text = re.sub(r'\s+', ' ', latex_text)

        print(f"🧪 Text Chemistry notation applied: {latex_text}")
        return latex_text.strip()

    def apply_chemistry_unicode_format(self, latex_text):
        """Unicode chemistry notation"""
        import re

        # Clean up first
        latex_text = self.clean_latex_for_chemistry(latex_text)

        # Unicode arrows
        latex_text = re.sub(r'-->', r' → ', latex_text)
        latex_text = re.sub(r'<-->', r' ⇌ ', latex_text)
        latex_text = re.sub(r'<--', r' ← ', latex_text)
        latex_text = re.sub(r'->', r' → ', latex_text)
        latex_text = re.sub(r'<->', r' ⇌ ', latex_text)

        # Unicode subscripts for numbers
        subscript_map = {
            '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄',
            '5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉'
        }

        def replace_subscripts(match):
            element = match.group(1)
            number = match.group(2)
            unicode_number = ''.join(subscript_map.get(digit, digit) for digit in number)
            return f"{element}{unicode_number}"

        latex_text = re.sub(r'([A-Z][a-z]?)(\d+)', replace_subscripts, latex_text)

        # Unicode symbols
        latex_text = re.sub(r'\\Delta|Delta', r'Δ', latex_text)
        latex_text = re.sub(r'\\rightleftharpoons', r'⇌', latex_text)
        latex_text = re.sub(r'\+\\s*heat', r' + Δ', latex_text)

        # States with unicode subscripts
        latex_text = re.sub(r'\(s\)', r'₍ₛ₎', latex_text)
        latex_text = re.sub(r'\(l\)', r'₍ₗ₎', latex_text)
        latex_text = re.sub(r'\(g\)', r'₍ₘ₎', latex_text)
        latex_text = re.sub(r'\(aq\)', r'₍ₐᵩ₎', latex_text)

        # Remove any remaining LaTeX commands
        latex_text = re.sub(r'\\[a-zA-Z]+\*?', '', latex_text)
        latex_text = re.sub(r'[{}]', '', latex_text)
        latex_text = re.sub(r'\s+', ' ', latex_text)

        print(f"🧪 Unicode Chemistry notation applied: {latex_text}")
        return latex_text.strip()

    def apply_physics_fixes(self, latex_text):
        """Apply physics-specific LaTeX fixes"""
        import re

        # Physics vectors
        latex_text = re.sub(r'\\vec\{([^}]+)\}', r'\\vec{\1}', latex_text)
        latex_text = re.sub(r'\\hat\{([^}]+)\}', r'\\hat{\1}', latex_text)

        # Physics constants and units
        latex_text = re.sub(r'\\mu', r'\\mu', latex_text)  # Micro
        latex_text = re.sub(r'\\omega', r'\\omega', latex_text)  # Angular frequency
        latex_text = re.sub(r'\\Omega', r'\\Omega', latex_text)  # Ohm

        # Common physics notation
        latex_text = re.sub(r'\\partial', r'\\partial', latex_text)  # Partial derivatives
        latex_text = re.sub(r'\\nabla', r'\\nabla', latex_text)  # Gradient

        # Units formatting
        latex_text = re.sub(r'(\d+)\s*(m|kg|s|A|K|mol|cd)\b', r'\1\\,\\text{\2}', latex_text)

        print(f"⚛️ Physics fixes applied: {latex_text}")
        return latex_text

    def apply_mathematics_fixes(self, latex_text):
        """Apply mathematics-specific LaTeX fixes"""
        import re

        # Mathematical operators
        latex_text = re.sub(r'\\int', r'\\int', latex_text)  # Integrals
        latex_text = re.sub(r'\\sum', r'\\sum', latex_text)  # Summation
        latex_text = re.sub(r'\\prod', r'\\prod', latex_text)  # Product

        # Mathematical symbols
        latex_text = re.sub(r'\\infty', r'\\infty', latex_text)  # Infinity
        latex_text = re.sub(r'\\pi', r'\\pi', latex_text)  # Pi
        latex_text = re.sub(r'\\theta', r'\\theta', latex_text)  # Theta

        # Set notation
        latex_text = re.sub(r'\\in', r'\\in', latex_text)  # Element of
        latex_text = re.sub(r'\\subset', r'\\subset', latex_text)  # Subset
        latex_text = re.sub(r'\\cup', r'\\cup', latex_text)  # Union
        latex_text = re.sub(r'\\cap', r'\\cap', latex_text)  # Intersection

        print(f"📐 Mathematics fixes applied: {latex_text}")
        return latex_text

    # Chemistry format selector removed - now only produces LaTeX code

    # ==================== SUBJECT-SPECIFIC METHODS ====================

    def initialize_subject_data(self, subject):
        """Initialize data structures for a specific subject"""
        # Initialize subject-specific file lists
        files_attr = f"current_files_{subject.lower()}"
        images_attr = f"current_images_{subject.lower()}"
        page_attr = f"current_page_{subject.lower()}"
        zoom_attr = f"zoom_factor_{subject.lower()}"
        selection_attr = f"current_selection_{subject.lower()}"

        if not hasattr(self, files_attr):
            setattr(self, files_attr, [])
        if not hasattr(self, images_attr):
            setattr(self, images_attr, [])
        if not hasattr(self, page_attr):
            setattr(self, page_attr, 0)
        if not hasattr(self, zoom_attr):
            setattr(self, zoom_attr, 1.0)
        if not hasattr(self, selection_attr):
            setattr(self, selection_attr, None)

    def import_subject_files(self, subject):
        """Import files for a specific subject"""
        print(f"📁 Importing files for {subject}...")

        filetypes = [
            ("All supported", "*.pdf *.png *.jpg *.jpeg *.bmp *.tiff"),
            ("PDF files", "*.pdf"),
            ("Image files", "*.png *.jpg *.jpeg *.bmp *.tiff"),
            ("All files", "*.*")
        ]

        filenames = filedialog.askopenfilenames(
            title=f"Import {subject} Files",
            filetypes=filetypes
        )

        if filenames:
            # Store in subject-specific attributes
            files_attr = f"current_files_{subject.lower()}"
            setattr(self, files_attr, list(filenames))

            # Load the files for this subject
            self.load_subject_files(subject)

            icon = {"Mathematics": "📐", "Chemistry": "🧪", "Physics": "⚛️"}.get(subject, "📐")
            messagebox.showinfo("Files Imported", f"{icon} {len(filenames)} {subject} files imported successfully!")

    def convert_pdf_to_images(self, pdf_path):
        """Convert PDF file to list of images"""
        images = []
        try:
            print(f"🔥 DEBUG: Converting PDF to images: {pdf_path}")
            pdf_images = convert_from_path(pdf_path, dpi=200)
            print(f"🔥 DEBUG: PDF converted to {len(pdf_images)} images")

            filename = os.path.basename(pdf_path)
            for i, img in enumerate(pdf_images):
                images.append({
                    'image': img,
                    'filename': f"{filename} - Page {i+1}",
                    'source_file': pdf_path,
                    'page_num': i+1
                })
                print(f"🔥 DEBUG: Added page {i+1} of {filename}")
        except Exception as e:
            print(f"🔥 DEBUG: PDF conversion error: {e}")

        return images

    def load_subject_files(self, subject):
        """Load files for a specific subject"""
        files_attr = f"current_files_{subject.lower()}"
        images_attr = f"current_images_{subject.lower()}"

        files = getattr(self, files_attr, [])
        if not files:
            return

        images = []
        for filename in files:
            try:
                if filename.lower().endswith('.pdf'):
                    # Convert PDF pages to images
                    pdf_images = self.convert_pdf_to_images(filename)
                    images.extend(pdf_images)
                else:
                    # Load image file
                    img = Image.open(filename)
                    images.append({
                        'image': img,
                        'filename': os.path.basename(filename),
                        'page_num': 1
                    })
            except Exception as e:
                print(f"Error loading {filename}: {e}")

        # Store images for this subject
        setattr(self, images_attr, images)

        # Update subject-specific file list display
        self.update_subject_file_list(subject)

        # Display first image for this subject
        if images:
            setattr(self, f"current_page_{subject.lower()}", 0)
            self.display_subject_image(subject)

    def update_subject_file_list(self, subject):
        """Update the file list display for a specific subject"""
        file_listbox_attr = f"file_listbox_{subject.lower()}"
        images_attr = f"current_images_{subject.lower()}"

        if not hasattr(self, file_listbox_attr):
            return

        file_listbox = getattr(self, file_listbox_attr)
        images = getattr(self, images_attr, [])

        # Clear current list
        file_listbox.delete(0, tk.END)

        # Add files to list
        for i, img_data in enumerate(images):
            display_name = f"Page {img_data['page_num']} - {img_data['filename']}"
            file_listbox.insert(tk.END, display_name)

        # Update page label
        page_label_attr = f"page_label_{subject.lower()}"
        if hasattr(self, page_label_attr):
            page_label = getattr(self, page_label_attr)
            if images:
                page_label.config(text=f"1 of {len(images)} files")
            else:
                page_label.config(text="No files loaded")

    def display_subject_image(self, subject):
        """Display current image for a specific subject"""
        images_attr = f"current_images_{subject.lower()}"
        page_attr = f"current_page_{subject.lower()}"
        canvas_attr = f"canvas_{subject.lower()}"
        zoom_attr = f"zoom_factor_{subject.lower()}"

        images = getattr(self, images_attr, [])
        current_page = getattr(self, page_attr, 0)

        if not images or current_page >= len(images):
            return

        canvas = getattr(self, canvas_attr, None)
        if not canvas:
            return

        # Get current image
        img_data = images[current_page]
        img = img_data['image']
        zoom_factor = getattr(self, zoom_attr, 1.0)

        # Resize image based on zoom
        display_width = int(img.width * zoom_factor)
        display_height = int(img.height * zoom_factor)
        resized_img = img.resize((display_width, display_height), Image.Resampling.LANCZOS)

        # Convert to PhotoImage
        photo = ImageTk.PhotoImage(resized_img)

        # Store reference to prevent garbage collection
        setattr(self, f"photo_{subject.lower()}", photo)

        # Clear canvas and display image
        canvas.delete("all")
        canvas.create_image(0, 0, anchor=tk.NW, image=photo)
        canvas.config(scrollregion=canvas.bbox("all"))

    def on_subject_file_select(self, event, subject):
        """Handle file selection for a specific subject"""
        file_listbox_attr = f"file_listbox_{subject.lower()}"
        file_listbox = getattr(self, file_listbox_attr, None)

        if not file_listbox:
            return

        selection = file_listbox.curselection()
        if selection:
            page_num = selection[0]
            setattr(self, f"current_page_{subject.lower()}", page_num)
            self.display_subject_image(subject)

    def prev_subject_page(self, subject):
        """Go to previous page for a specific subject"""
        page_attr = f"current_page_{subject.lower()}"
        images_attr = f"current_images_{subject.lower()}"

        current_page = getattr(self, page_attr, 0)
        images = getattr(self, images_attr, [])

        if current_page > 0:
            setattr(self, page_attr, current_page - 1)
            self.display_subject_image(subject)
            self.update_subject_page_label(subject)

    def next_subject_page(self, subject):
        """Go to next page for a specific subject"""
        page_attr = f"current_page_{subject.lower()}"
        images_attr = f"current_images_{subject.lower()}"

        current_page = getattr(self, page_attr, 0)
        images = getattr(self, images_attr, [])

        if current_page < len(images) - 1:
            setattr(self, page_attr, current_page + 1)
            self.display_subject_image(subject)
            self.update_subject_page_label(subject)

    def update_subject_page_label(self, subject):
        """Update page label for a specific subject"""
        page_label_attr = f"page_label_{subject.lower()}"
        page_attr = f"current_page_{subject.lower()}"
        images_attr = f"current_images_{subject.lower()}"

        if not hasattr(self, page_label_attr):
            return

        page_label = getattr(self, page_label_attr)
        current_page = getattr(self, page_attr, 0)
        images = getattr(self, images_attr, [])

        if images:
            page_label.config(text=f"{current_page + 1} of {len(images)} files")
        else:
            page_label.config(text="No files loaded")

    def export_subject_to_word(self, subject):
        """Export equations to Word document for a specific subject"""
        queue_attr = f"equation_queue_{subject.lower()}"
        subject_queue = getattr(self, queue_attr, [])

        if not subject_queue:
            messagebox.showwarning("Empty Queue", f"No {subject} equations to export")
            return

        # Use existing export functionality but with subject-specific queue
        icon = {"Mathematics": "📐", "Chemistry": "🧪", "Physics": "⚛️"}.get(subject, "📐")

        filename = filedialog.asksaveasfilename(
            title=f"Export {subject} Equations",
            defaultextension=".docx",
            filetypes=[("Word documents", "*.docx"), ("All files", "*.*")]
        )

        if filename:
            try:
                # Create Word document with subject-specific content
                self.create_subject_word_document(filename, subject, subject_queue)
                messagebox.showinfo("Export Complete", f"{icon} {subject} equations exported to {filename}")
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export {subject} equations: {str(e)}")

    # ==================== SUBJECT-SPECIFIC SELECTION & ZOOM ====================

    def start_subject_selection(self, event, subject):
        """Start region selection for a specific subject"""
        canvas_attr = f"canvas_{subject.lower()}"
        canvas = getattr(self, canvas_attr, None)
        if not canvas:
            return

        start_attr = f"selection_start_{subject.lower()}"
        setattr(self, start_attr, (canvas.canvasx(event.x), canvas.canvasy(event.y)))

    def update_subject_selection(self, event, subject):
        """Update selection rectangle for a specific subject"""
        canvas_attr = f"canvas_{subject.lower()}"
        start_attr = f"selection_start_{subject.lower()}"
        rect_attr = f"selection_rect_{subject.lower()}"

        canvas = getattr(self, canvas_attr, None)
        selection_start = getattr(self, start_attr, None)

        if not canvas or not selection_start:
            return

        current_pos = (canvas.canvasx(event.x), canvas.canvasy(event.y))

        # Remove previous rectangle
        selection_rect = getattr(self, rect_attr, None)
        if selection_rect:
            canvas.delete(selection_rect)

        # Draw new rectangle
        new_rect = canvas.create_rectangle(
            selection_start[0], selection_start[1],
            current_pos[0], current_pos[1],
            outline='red', width=2
        )
        setattr(self, rect_attr, new_rect)

    def end_subject_selection(self, event, subject):
        """End region selection for a specific subject"""
        canvas_attr = f"canvas_{subject.lower()}"
        start_attr = f"selection_start_{subject.lower()}"
        selection_attr = f"current_selection_{subject.lower()}"

        canvas = getattr(self, canvas_attr, None)
        selection_start = getattr(self, start_attr, None)

        if not canvas or not selection_start:
            return

        current_pos = (canvas.canvasx(event.x), canvas.canvasy(event.y))

        # Calculate selection bounds
        x1, y1 = selection_start
        x2, y2 = current_pos

        # Ensure proper ordering
        x, y = min(x1, x2), min(y1, y2)
        width, height = abs(x2 - x1), abs(y2 - y1)

        if width > 10 and height > 10:  # Minimum selection size
            # Store selection for OCR processing
            selection = {
                'x': int(x), 'y': int(y),
                'width': int(width), 'height': int(height)
            }
            setattr(self, selection_attr, selection)
            print(f"📐 {subject} selection: {selection}")

    def on_subject_mouse_wheel(self, event, subject):
        """Handle mouse wheel zoom for a specific subject"""
        zoom_attr = f"zoom_factor_{subject.lower()}"
        zoom_factor = getattr(self, zoom_attr, 1.0)

        # Determine zoom direction
        if event.delta > 0 or event.num == 4:
            zoom_factor *= 1.1
        else:
            zoom_factor /= 1.1

        # Limit zoom range
        zoom_factor = max(0.1, min(zoom_factor, 5.0))
        setattr(self, zoom_attr, zoom_factor)

        # Redisplay image with new zoom
        self.display_subject_image(subject)

    def zoom_subject_in(self, subject):
        """Zoom in for a specific subject"""
        zoom_attr = f"zoom_factor_{subject.lower()}"
        zoom_factor = getattr(self, zoom_attr, 1.0)
        zoom_factor = min(zoom_factor * 1.2, 5.0)
        setattr(self, zoom_attr, zoom_factor)
        self.display_subject_image(subject)

    def zoom_subject_out(self, subject):
        """Zoom out for a specific subject"""
        zoom_attr = f"zoom_factor_{subject.lower()}"
        zoom_factor = getattr(self, zoom_attr, 1.0)
        zoom_factor = max(zoom_factor / 1.2, 0.1)
        setattr(self, zoom_attr, zoom_factor)
        self.display_subject_image(subject)

    def fit_subject_to_window(self, subject):
        """Fit image to window for a specific subject"""
        canvas_attr = f"canvas_{subject.lower()}"
        images_attr = f"current_images_{subject.lower()}"
        page_attr = f"current_page_{subject.lower()}"

        canvas = getattr(self, canvas_attr, None)
        images = getattr(self, images_attr, [])
        current_page = getattr(self, page_attr, 0)

        if not canvas or not images or current_page >= len(images):
            return

        # Get current image
        img_data = images[current_page]
        img = img_data['image']

        # Get canvas size
        canvas.update_idletasks()
        canvas_width = canvas.winfo_width()
        canvas_height = canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            # Canvas not ready, use default
            setattr(self, f"zoom_factor_{subject.lower()}", 1.0)
        else:
            # Calculate zoom to fit
            zoom_x = canvas_width / img.width
            zoom_y = canvas_height / img.height
            zoom_factor = min(zoom_x, zoom_y, 1.0)  # Don't zoom in beyond 100%

            setattr(self, f"zoom_factor_{subject.lower()}", zoom_factor)

        self.display_subject_image(subject)
        print(f"🔧 Fit to window applied for {subject}")

    def create_subject_word_document(self, filename, subject, equations):
        """Create Word document with subject-specific formatting"""
        try:
            from docx import Document
            from docx.shared import Inches

            doc = Document()

            # Add title
            icon = {"Mathematics": "📐", "Chemistry": "🧪", "Physics": "⚛️"}.get(subject, "📐")
            title = doc.add_heading(f'{icon} {subject} Equations', 0)

            # Add equations
            for i, eq in enumerate(equations):
                # Add equation number
                doc.add_heading(f'Equation {i+1}', level=2)

                # Add LaTeX code
                p = doc.add_paragraph()
                p.add_run('LaTeX: ').bold = True
                p.add_run(eq.latex_text)

                # Add metadata
                if hasattr(eq, 'filename') and eq.filename:
                    p = doc.add_paragraph()
                    p.add_run('Source: ').bold = True
                    p.add_run(f"{eq.filename} (Page {eq.page_num})")

                if hasattr(eq, 'confidence') and eq.confidence:
                    p = doc.add_paragraph()
                    p.add_run('Confidence: ').bold = True
                    p.add_run(f"{eq.confidence:.1f}%")

                # Add spacing
                doc.add_paragraph()

            # Save document
            doc.save(filename)

        except ImportError:
            # Fallback to text file if python-docx not available
            with open(filename.replace('.docx', '.txt'), 'w', encoding='utf-8') as f:
                f.write(f"{subject} Equations\n")
                f.write("=" * 50 + "\n\n")

                for i, eq in enumerate(equations):
                    f.write(f"Equation {i+1}:\n")
                    f.write(f"LaTeX: {eq.latex_text}\n")
                    if hasattr(eq, 'filename'):
                        f.write(f"Source: {eq.filename} (Page {eq.page_num})\n")
                    if hasattr(eq, 'confidence'):
                        f.write(f"Confidence: {eq.confidence:.1f}%\n")
                    f.write("\n" + "-" * 30 + "\n\n")

    def perfect_latex_postprocess(self, latex_text):
        """
        Optional AI-powered mathematical content processor with fast fallback

        Uses AI reasoning when enabled, otherwise uses fast rule-based processing.
        """
        try:
            # Check AI settings
            ai_enabled = self.settings.get('ai_enabled', False)
            math_reasoning_enabled = self.settings.get('ai_math_reasoning_enabled', False)

            # Use AI reasoning only if enabled
            if ai_enabled and math_reasoning_enabled:
                # Try Local LLM first
                if hasattr(self, 'local_llm') and self.local_llm.is_available():
                    print("🤖 Using Local LLM for mathematical processing...")
                    result = self.local_llm.process_mathematical_expression(latex_text)

                    print(f"🤖 LOCAL LLM ANALYSIS:")
                    print(f"   Reasoning: {result.get('reasoning', 'No reasoning available')}")
                    print(f"   Confidence: {result.get('confidence', 0)}%")

                    return result.get('latex', latex_text)

                else:
                    print("🔄 Local LLM not available, using rule-based AI fallback...")
                    # Import the rule-based AI processor as fallback
                    from ai_math_processor import AIMathProcessor

                    # Initialize rule-based processor
                    ai_processor = AIMathProcessor()

                    # Process with rule-based reasoning
                    result = ai_processor.process_mathematical_expression(latex_text)

                    print(f"🤖 RULE-BASED ANALYSIS:")
                    print(f"   Reasoning: {result.get('reasoning', 'No reasoning available')}")
                    print(f"   Confidence: {result.get('confidence', 0)}%")

                    # Get the processed LaTeX
                    ai_latex = result.get('latex', latex_text)

                    # Apply minimal cleanup
                    final_latex = self._minimal_cleanup(ai_latex)

                    return final_latex
            else:
                print("🚀 AI math reasoning disabled - using fast basic processing")
                # Skip AI processing for faster response
                return self._basic_fallback_cleanup(latex_text)

        except Exception as e:
            print(f"🤖 AI processing failed: {e}")
            print("🔄 Falling back to basic cleanup...")

            # Ultimate fallback to basic cleanup with Word compatibility
            return self._basic_fallback_cleanup(latex_text)

    def _minimal_cleanup(self, latex_text):
        """Minimal cleanup after AI processing"""
        import re

        # Just basic whitespace normalization
        latex_text = re.sub(r'\s+', ' ', latex_text).strip()

        # Remove empty braces
        latex_text = re.sub(r'\{\}', '', latex_text)

        return latex_text

    def _basic_fallback_cleanup(self, latex_text):
        """Basic fallback cleanup if AI processing fails - with Word compatibility"""
        import re

        if not latex_text or not latex_text.strip():
            return ""

        # Very basic cleanup
        latex_text = latex_text.strip()

        # CRITICAL: Fix Word compatibility issues first
        latex_text = self.fix_word_compatibility(latex_text)

        # Remove array wrappers
        latex_text = re.sub(r'\\begin\{array\}.*?\{([^}]*)\}&\{([^}]*)\}\\end\{array\}', r'\1 \2', latex_text)

        # Basic operator spacing
        latex_text = re.sub(r'\s*=\s*', ' = ', latex_text)
        latex_text = re.sub(r'\s*>\s*', ' > ', latex_text)
        latex_text = re.sub(r'\s*<\s*', ' < ', latex_text)

        # Basic arrow conversion
        latex_text = re.sub(r'\\Rightarrow', r' \\Rightarrow ', latex_text)

        # Clean up spaces
        latex_text = re.sub(r'\s+', ' ', latex_text).strip()

        return latex_text

    def fix_word_compatibility(self, latex_text):
        """Fix LaTeX to be compatible with Microsoft Word equation editor"""
        import re

        if not latex_text or not latex_text.strip():
            return ""

        print(f"🔧 Word compatibility input: {latex_text}")

        # 1. Remove ALL array environments (Word doesn't support them)
        # Handle the specific pattern from your screenshot first
        latex_text = re.sub(r'\\begin\{array\}\{[^}]*\}([^&]*?)&\{([^}]*?)\}\\end\{array\}', r'\1 \2', latex_text)

        # Handle simpler array patterns
        latex_text = re.sub(r'\\begin\{array\}\{[^}]*\}(.*?)\\end\{array\}', r'\1', latex_text, flags=re.DOTALL)
        latex_text = re.sub(r'\\begin\{array\}(.*?)\\end\{array\}', r'\1', latex_text, flags=re.DOTALL)

        # 2. Remove other unsupported environments
        unsupported_envs = ['aligned', 'align', 'gather', 'multline', 'split']
        for env in unsupported_envs:
            latex_text = re.sub(rf'\\begin\{{{env}\*?\}}(.*?)\\end\{{{env}\*?\}}', r'\1', latex_text, flags=re.DOTALL)

        # 3. Fix column specifications and alignment characters
        latex_text = re.sub(r'\{[rlc]+\}', '', latex_text)  # Remove column specs like {rl}
        latex_text = re.sub(r'&', ' ', latex_text)  # Remove alignment characters

        # 4. Remove unsupported sizing commands
        latex_text = re.sub(r'\\[Bb]igg?[lr]?', '', latex_text)

        # 5. Simplify complex structures that Word can't handle
        latex_text = re.sub(r'\\left\\{', '{', latex_text)
        latex_text = re.sub(r'\\right\\}', '}', latex_text)

        # 6. Clean up multiple spaces and formatting
        latex_text = re.sub(r'\s+', ' ', latex_text)
        latex_text = latex_text.strip()

        # 7. Remove any remaining braces around simple expressions
        latex_text = re.sub(r'^\{(.*)\}$', r'\1', latex_text)

        # 8. Add proper spacing around operators for Word
        latex_text = re.sub(r'\s*>\s*', r' > ', latex_text)
        latex_text = re.sub(r'\s*<\s*', r' < ', latex_text)
        latex_text = re.sub(r'\s*=\s*', r' = ', latex_text)
        latex_text = re.sub(r'\s*\\Rightarrow\s*', r' \\Rightarrow ', latex_text)

        # 9. Add multiplication symbols where needed for clarity
        latex_text = re.sub(r'(\d+)\(', r'\1 \\cdot (', latex_text)
        latex_text = re.sub(r'\\mu\(', r'\\mu \\cdot (', latex_text)

        # 10. Final cleanup
        latex_text = re.sub(r'\s+', ' ', latex_text).strip()

        print(f"🔧 Word compatibility output: {latex_text}")

        return latex_text

    def _extract_from_arrays(self, latex_text):
        """Extract equations from LaTeX array structures while preserving content"""
        import re

        # Pattern 1: \begin{array}{...}{\symbol}&{equation}\end{array}
        array_pattern = r'\\begin\{array\}[^}]*\}\{([^}]*)\}&\{([^}]*)\}\\end\{array\}'
        match = re.search(array_pattern, latex_text)
        if match:
            symbol = match.group(1)
            equation = match.group(2)
            # Preserve the symbol if it's meaningful (like \Rightarrow)
            if symbol and '\\' in symbol:
                return f"{symbol} {equation}"
            else:
                return equation

        # Pattern 2: Other array-like structures
        latex_text = re.sub(r'\\begin\{[^}]+\}', '', latex_text)
        latex_text = re.sub(r'\\end\{[^}]+\}', '', latex_text)

        return latex_text

    def _fix_malformed_structures(self, latex_text):
        """Fix common LaTeX structural issues"""
        import re

        # Remove empty braces
        latex_text = re.sub(r'\{\}', '', latex_text)

        # Fix malformed sizing commands
        sizing_commands = ['bigg', 'Bigg', 'big', 'Big', 'left', 'right']
        for cmd in sizing_commands:
            latex_text = re.sub(rf'\{{\\{cmd}\(\}}', rf'\\{cmd}(', latex_text)
            latex_text = re.sub(rf'\{{\\{cmd}\)\}}', rf'\\{cmd})', latex_text)
            latex_text = re.sub(rf'\{{\\{cmd}\[\}}', rf'\\{cmd}[', latex_text)
            latex_text = re.sub(rf'\{{\\{cmd}\]\}}', rf'\\{cmd}]', latex_text)

        # Fix malformed fractions and roots
        latex_text = re.sub(r'\{\\frac\{([^}]+)\}\{([^}]+)\}\}', r'\\frac{\1}{\2}', latex_text)
        latex_text = re.sub(r'\{\\sqrt\{([^}]+)\}\}', r'\\sqrt{\1}', latex_text)

        # Fix nested braces issues
        latex_text = re.sub(r'\{\{([^}]+)\}\}', r'{\1}', latex_text)

        return latex_text

    def _normalize_operators(self, latex_text):
        """Normalize spacing around mathematical operators"""
        import re

        # Standard operators with proper spacing
        operators = [
            ('=', ' = '), ('>', ' > '), ('<', ' < '),
            (r'\\leq', r' \\leq '), (r'\\geq', r' \\geq '), (r'\\neq', r' \\neq '),
            (r'\\approx', r' \\approx '), (r'\\equiv', r' \\equiv '),
            (r'\\Rightarrow', r' \\Rightarrow '), (r'\\Leftarrow', r' \\Leftarrow '),
            (r'\\rightarrow', r' \\rightarrow '), (r'\\leftarrow', r' \\leftarrow '),
            (r'\\pm', r' \\pm '), (r'\\mp', r' \\mp ')
        ]

        for op, spaced_op in operators:
            if op.startswith(r'\\'):
                # For LaTeX commands, use raw string matching
                latex_text = re.sub(rf'\s*{op}\s*', spaced_op, latex_text)
            else:
                # For simple operators, escape them
                latex_text = re.sub(rf'\s*{re.escape(op)}\s*', spaced_op, latex_text)

        return latex_text

    def _fix_mathematical_functions(self, latex_text):
        """Ensure mathematical functions are properly formatted"""
        import re

        # Common mathematical functions
        functions = [
            'sin', 'cos', 'tan', 'cot', 'sec', 'csc',
            'arcsin', 'arccos', 'arctan', 'sinh', 'cosh', 'tanh',
            'log', 'ln', 'exp', 'det', 'lim', 'max', 'min', 'sup', 'inf'
        ]

        for func in functions:
            # Ensure functions are properly escaped
            latex_text = re.sub(rf'\\{func}\b', rf'\\{func}', latex_text)
            # Fix spacing around function arguments
            latex_text = re.sub(rf'\\{func}\s*\(', rf'\\{func}(', latex_text)

        return latex_text

    def _add_multiplication_symbols(self, latex_text):
        """Intelligently add multiplication symbols where needed"""
        import re

        # Add \cdot between number and parenthesis (like 50(...))
        latex_text = re.sub(r'(\d+)\s*\(', r'\1 \\cdot (', latex_text)

        # Add \cdot between variable and parenthesis (like x(...))
        latex_text = re.sub(r'([a-zA-Z])\s*\(', r'\1 \\cdot (', latex_text)

        # Add \cdot between number and variable (like 25x)
        latex_text = re.sub(r'(\d+)\s*([a-zA-Z])', r'\1 \\cdot \2', latex_text)

        # Add \cdot between number and LaTeX command (like 25\sqrt)
        latex_text = re.sub(r'(\d+)\s*(\\[a-zA-Z]+)', r'\1 \\cdot \2', latex_text)

        # Clean up multiple \cdot
        latex_text = re.sub(r'\\cdot\s+\\cdot', r'\\cdot', latex_text)

        return latex_text

    def _final_cleanup(self, latex_text):
        """Final cleanup and normalization"""
        import re

        # Normalize spacing
        latex_text = re.sub(r'\s+', ' ', latex_text)

        # Fix spacing around \cdot
        latex_text = re.sub(r'\s*\\cdot\s*', r' \\cdot ', latex_text)

        # Remove leading/trailing spaces
        latex_text = latex_text.strip()

        # Remove any remaining problematic patterns
        latex_text = re.sub(r'\s*\\\s*', r'\\', latex_text)  # Fix broken commands

        return latex_text

    def fix_latex_manually(self):
        """Manual LaTeX fixing function - immediate solution"""
        print("🔥 DEBUG: fix_latex_manually called")
        import sys
        sys.stdout.flush()

        # Get current LaTeX text
        current_latex = self.latex_text.get(1.0, tk.END).strip()

        if not current_latex:
            messagebox.showwarning("No LaTeX", "Please enter some LaTeX code first or process an equation with OCR.")
            return

        print(f"🔥 DEBUG: Manual fix input: '{current_latex}'")
        sys.stdout.flush()

        # Detect pitfalls before fixing
        pitfalls = self.detect_latex_pitfalls(current_latex)

        # Apply comprehensive LaTeX fixing
        fixed_latex = self.comprehensive_latex_fix(current_latex)

        # Update the LaTeX editor
        self.latex_text.delete(1.0, tk.END)
        self.latex_text.insert(1.0, fixed_latex)

        # Update OCR text to show what was fixed
        fixes_applied = self.get_fixes_applied(current_latex, fixed_latex)
        self.ocr_text.delete(1.0, tk.END)
        self.ocr_text.insert(1.0, f"[Manual Fix] {fixes_applied}")

        print(f"🔥 DEBUG: Manual fix output: '{fixed_latex}'")
        sys.stdout.flush()

        # Show detailed fix report
        self.show_fix_report(pitfalls, current_latex, fixed_latex)

    def comprehensive_latex_fix(self, latex_text):
        """Comprehensive LaTeX fixing for all common issues"""
        import re

        print(f"🔧 Fixing LaTeX: {latex_text}")

        # Step 1: Fix the specific issues from your screenshot
        latex_text = self.fix_screenshot_issues(latex_text)

        # Step 2: Apply general post-processing
        latex_text = self.perfect_latex_postprocess(latex_text)

        # Step 3: Final validation and cleanup
        latex_text = self.final_latex_validation(latex_text)

        return latex_text

    def fix_screenshot_issues(self, latex_text):
        """Fix the specific issues visible in the screenshot"""
        import re

        # Fix array syntax errors: {r 1} → {rl}
        latex_text = re.sub(r'\{r\s+1\}', r'{rl}', latex_text)
        latex_text = re.sub(r'\{r\s+l\}', r'{rl}', latex_text)

        # Fix malformed commands
        latex_text = re.sub(r'\\fFrac', r'\\frac', latex_text)  # \fFrac → \frac
        latex_text = re.sub(r'\\endfarray', r'\\end{array}', latex_text)  # \endfarray → \end{array}

        # Fix missing braces in fractions
        latex_text = re.sub(r'\\frac\s*(\d+)\s*(\d+)', r'\\frac{\1}{\2}', latex_text)

        # Fix sqrt syntax
        latex_text = re.sub(r'\{\\sqrt\{(\d+)\}\}', r'\\sqrt{\1}', latex_text)

        # Fix mu symbol spacing
        latex_text = re.sub(r'\\mu\s*\(', r'\\mu \\cdot (', latex_text)

        # Fix number-parentheses multiplication
        latex_text = re.sub(r'(\d+)\s*\(', r'\1 \\cdot (', latex_text)

        # Fix operator spacing
        latex_text = re.sub(r'\s*>\s*', r' > ', latex_text)
        latex_text = re.sub(r'\s*=\s*', r' = ', latex_text)

        # Apply common pitfall fixes
        latex_text = self.fix_common_pitfalls(latex_text)

        return latex_text

    def fix_common_pitfalls(self, latex_text):
        """Fix common LaTeX pitfalls and typos"""
        import re

        # 1. Arrow symbol corrections
        # \rightarrow vs \Rightarrow - context-aware fixing
        # For implications, use \Rightarrow; for functions, use \rightarrow
        if any(word in latex_text.lower() for word in ['implies', 'therefore', 'hence', 'thus']):
            latex_text = re.sub(r'\\rightarrow', r'\\Rightarrow', latex_text)

        # 2. Fix incorrect spacing in mathematical expressions
        # 25\sqrt{3} is correct, 2 5\sqrt{3} is wrong
        latex_text = re.sub(r'(\d+)\s+(\d+)\\sqrt', r'\1\2\\sqrt', latex_text)  # 2 5\sqrt → 25\sqrt
        latex_text = re.sub(r'(\d+)\s+(\d+)\\', r'\1\2\\', latex_text)  # General number spacing

        # 3. Environment optimization
        # Single-line equations: prefer \[ ... \] over align*
        if latex_text.count('\\\\') == 0 and 'align' in latex_text:
            # Single line in align* environment - convert to \[ ... \]
            latex_text = re.sub(r'\\begin\{align\*\}\s*(.*?)\s*\\end\{align\*\}', r'\\[ \1 \\]', latex_text, flags=re.DOTALL)
            latex_text = re.sub(r'\\begin\{aligned\}\s*(.*?)\s*\\end\{aligned\}', r'\\[ \1 \\]', latex_text, flags=re.DOTALL)

        # 4. Fix common symbol typos
        symbol_fixes = {
            r'\\infty': r'\\infty',  # Ensure correct infinity
            r'\\alpha': r'\\alpha',  # Common Greek letters
            r'\\beta': r'\\beta',
            r'\\gamma': r'\\gamma',
            r'\\delta': r'\\delta',
            r'\\epsilon': r'\\varepsilon',  # Prefer varepsilon
            r'\\phi': r'\\varphi',  # Prefer varphi
            r'\\theta': r'\\theta',
        }

        # 5. Fix spacing around operators
        latex_text = re.sub(r'\\pm\s*', r'\\pm ', latex_text)  # ± spacing
        latex_text = re.sub(r'\\mp\s*', r'\\mp ', latex_text)  # ∓ spacing
        latex_text = re.sub(r'\\times\s*', r'\\times ', latex_text)  # × spacing
        latex_text = re.sub(r'\\div\s*', r'\\div ', latex_text)  # ÷ spacing

        # 6. Fix parentheses sizing
        # Auto-detect when to use \left( \right)
        if any(cmd in latex_text for cmd in ['\\frac', '\\sqrt', '\\sum', '\\int']):
            # Replace regular parentheses with auto-sizing ones around complex expressions
            latex_text = re.sub(r'\(([^()]*(?:\\frac|\\sqrt|\\sum|\\int)[^()]*)\)', r'\\left(\1\\right)', latex_text)

        return latex_text

    def final_latex_validation(self, latex_text):
        """Final validation and cleanup of LaTeX"""
        import re

        # Remove extra spaces
        latex_text = re.sub(r'\s+', ' ', latex_text)

        # Ensure proper brace matching
        latex_text = self.fix_brace_matching(latex_text)

        # Clean up any remaining issues
        latex_text = latex_text.strip()

        return latex_text

    def fix_brace_matching(self, latex_text):
        """Ensure proper brace matching in LaTeX"""
        # Count braces and fix simple mismatches
        open_braces = latex_text.count('{')
        close_braces = latex_text.count('}')

        if open_braces > close_braces:
            # Add missing closing braces
            latex_text += '}' * (open_braces - close_braces)
        elif close_braces > open_braces:
            # Remove extra closing braces (simple approach)
            extra_closes = close_braces - open_braces
            for _ in range(extra_closes):
                latex_text = latex_text.replace('}', '', 1)

        return latex_text

    def detect_latex_pitfalls(self, latex_text):
        """Detect common LaTeX pitfalls and typos"""
        import re

        pitfalls = []

        # 1. Arrow symbol issues
        if '\\rightarrow' in latex_text and any(word in latex_text.lower() for word in ['implies', 'therefore']):
            pitfalls.append({
                'type': 'arrow_symbol',
                'issue': 'Using \\rightarrow for logical implication',
                'suggestion': 'Use \\Rightarrow for logical implications'
            })

        # 2. Spacing issues in numbers
        if re.search(r'\d+\s+\d+\\', latex_text):
            pitfalls.append({
                'type': 'number_spacing',
                'issue': 'Incorrect spacing in numbers (e.g., "2 5\\sqrt{3}")',
                'suggestion': 'Remove spaces between digits (e.g., "25\\sqrt{3}")'
            })

        # 3. Environment misuse
        if 'align*' in latex_text and latex_text.count('\\\\') == 0:
            pitfalls.append({
                'type': 'environment_misuse',
                'issue': 'Using align* for single-line equation',
                'suggestion': 'Use \\[ ... \\] for single-line equations'
            })

        # 4. Missing multiplication symbols
        if re.search(r'\\[a-zA-Z]+\(', latex_text) or re.search(r'\d+\(', latex_text):
            pitfalls.append({
                'type': 'missing_multiplication',
                'issue': 'Missing multiplication symbols',
                'suggestion': 'Add \\cdot between variables and parentheses'
            })

        # 5. Array syntax errors
        if re.search(r'\{r\s+\d+\}', latex_text):
            pitfalls.append({
                'type': 'array_syntax',
                'issue': 'Invalid array column specification',
                'suggestion': 'Use {rl} instead of {r 1}'
            })

        # 6. Malformed commands
        malformed_commands = ['\\fFrac', '\\endfarray', '\\beginfarray']
        for cmd in malformed_commands:
            if cmd in latex_text:
                pitfalls.append({
                    'type': 'malformed_command',
                    'issue': f'Malformed command: {cmd}',
                    'suggestion': f'Correct command syntax needed'
                })

        return pitfalls

    def get_fixes_applied(self, original, fixed):
        """Get a summary of fixes applied"""
        fixes = []

        if '\\cdot' in fixed and '\\cdot' not in original:
            fixes.append("Added multiplication symbols")

        if '{rl}' in fixed and '{r 1}' in original:
            fixes.append("Fixed array syntax")

        if '\\frac' in fixed and '\\fFrac' in original:
            fixes.append("Corrected \\frac command")

        if '\\end{array}' in fixed and '\\endfarray' in original:
            fixes.append("Fixed array environment")

        if ' > ' in fixed and '>' in original and ' > ' not in original:
            fixes.append("Fixed operator spacing")

        if not fixes:
            fixes.append("LaTeX validated and optimized")

        return ", ".join(fixes)

    def show_fix_report(self, pitfalls, original, fixed):
        """Show a detailed report of fixes applied"""
        report = "🔧 LaTeX Fix Report\n" + "="*40 + "\n\n"

        if pitfalls:
            report += "🚨 Issues Detected:\n"
            for i, pitfall in enumerate(pitfalls, 1):
                report += f"{i}. {pitfall['issue']}\n"
                report += f"   💡 {pitfall['suggestion']}\n\n"
        else:
            report += "✅ No major issues detected\n\n"

        report += f"📥 Original: {original[:100]}{'...' if len(original) > 100 else ''}\n\n"
        report += f"✅ Fixed:    {fixed[:100]}{'...' if len(fixed) > 100 else ''}\n\n"

        fixes_applied = self.get_fixes_applied(original, fixed)
        report += f"🎯 Fixes Applied: {fixes_applied}"

        messagebox.showinfo("LaTeX Fixed!", report)

    def clean_ocr_text(self, text):
        """Clean up common OCR errors in mathematical text with robust pattern matching"""
        import re

        # First pass: Basic character corrections
        text = self.apply_basic_corrections(text)

        # Second pass: Mathematical pattern recognition and correction
        text = self.apply_mathematical_corrections(text)

        # Third pass: Context-aware cleaning
        text = self.apply_contextual_cleaning(text)

        return text

    def apply_basic_corrections(self, text):
        """Apply basic character-level corrections with enhanced OCR error fixing"""
        import re

        # More aggressive character corrections for mathematical content
        corrections = [
            # Critical OCR fixes for your specific issues
            (r'\b4D\b', '40'),    # Fix "4D" -> "40" (specific to your problem)
            (r'\bAD\b', '40'),    # Fix "AD" -> "40"
            (r'\b4O\b', '40'),    # Fix "4O" -> "40"

            # Common OCR character confusions - more aggressive
            (r'\bl(?=\d|/)', '1'),  # l followed by digit or slash -> 1
            (r'\bI(?=\d|/)', '1'),  # I followed by digit or slash -> 1
            (r'\|(?=\d)', '1'),   # | followed by digit -> 1
            (r'\bO(?=\d)', '0'),  # O followed by digit -> 0
            (r'\b0(?=[a-zA-Z])', 'O'),  # 0 followed by letter -> O
            (r'\bS(?=\d)', '5'),  # S followed by digit -> 5
            (r'\bG(?=\d)', '6'),  # G followed by digit -> 6
            (r'\bB(?=\d)', '8'),  # B followed by digit -> 8

            # Mathematical symbols
            (r'<=', '≤'),
            (r'>=', '≥'),
            (r'!=', '≠'),
            (r'\+-', '±'),
            (r'=>', '⇒'),
            (r'->', '→'),
            (r'<-', '←'),

            # Square root symbol - more aggressive detection
            (r'\bV(?=\d)', '√'),  # V followed by digit -> square root
            (r'\bV(?=\()', '√'),  # V followed by parenthesis -> square root
            (r'(?<=\s)V(?=\d)', '√'),  # V after space followed by digit -> square root
            (r'(?<=\d)V(?=\d)', '√'),  # V between digits -> square root
            (r'sqrt', '√'),
            (r'SQRT', '√'),

            # Fix common letter/number confusions in mathematical context
            (r'\bl(?=/)', '1'),   # l before slash -> 1 (for fractions)
            (r'(?<=\d)l(?=\d)', '1'),  # l between digits -> 1

            # Greek letter recognition improvements
            (r'\bu\b(?=\s*[<>=])', 'μ'),  # u before comparison -> μ
            (r'\bu(?=\()', 'μ'),  # u before parenthesis -> μ
        ]

        for pattern, replacement in corrections:
            text = re.sub(pattern, replacement, text)

        return text

    def apply_mathematical_corrections(self, text):
        """Apply mathematical pattern corrections"""
        import re

        # Fix common mathematical expressions
        corrections = [
            # Trigonometric functions
            (r'\b(cos|sin|tan)\s*(\d+)\s*°', r'\1(\2°)'),
            (r'\b(cos|sin|tan)\s*(\d+)', r'\1(\2°)'),
            (r'\b(COS|SIN|TAN)\s*(\d+)', r'\1(\2°)'.lower()),

            # Degree symbols
            (r'(\d+)\s*degrees?', r'\1°'),
            (r'(\d+)\s*deg', r'\1°'),

            # Fractions - be more careful with detection
            (r'(\d+)\s*/\s*(\d+)', r'\1/\2'),  # Remove spaces in simple fractions

            # Square roots
            (r'√\s*(\d+)', r'√\1'),  # Remove space after square root
            (r'(\d+)\s*√\s*(\d+)', r'\1√\2'),  # Fix "25 √ 3" -> "25√3"

            # Mathematical operators
            (r'(\d+)\s*[xX*]\s*(\d+)', r'\1 × \2'),  # Multiplication
            (r'([a-zA-Z])\s*[xX*]\s*([a-zA-Z])', r'\1 × \2'),  # Variable multiplication

            # Equations and inequalities
            (r'(\w+)\s*=\s*(\w+)', r'\1 = \2'),
            (r'(\d+)\s*>\s*([^>]+)', r'\1 > \2'),
            (r'(\d+)\s*<\s*([^<]+)', r'\1 < \2'),

            # Greek letters (common misrecognitions)
            (r'\bu\b', 'μ'),  # u -> μ in mathematical context
            (r'\bmu\b', 'μ'),  # mu -> μ
            (r'\balpha\b', 'α'),
            (r'\bbeta\b', 'β'),
            (r'\btheta\b', 'θ'),
        ]

        for pattern, replacement in corrections:
            text = re.sub(pattern, replacement, text)

        return text

    def apply_contextual_cleaning(self, text):
        """Apply context-aware cleaning"""
        import re

        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)

        # Fix parentheses spacing
        text = re.sub(r'\(\s+', '(', text)
        text = re.sub(r'\s+\)', ')', text)

        # Fix common word boundaries
        text = re.sub(r'\b([a-zA-Z]+)(\d+)', r'\1 \2', text)  # Add space between letters and numbers
        text = re.sub(r'(\d+)([a-zA-Z]+)', r'\1 \2', text)    # Add space between numbers and letters

        # Clean up multiple operators
        text = re.sub(r'([=<>])\s*([=<>])', r'\1\2', text)  # Fix "= =" -> "=="

        return text.strip()

    def clean_mathematical_expressions(self, text):
        """Additional cleaning specifically for mathematical expressions"""
        import re

        # Fix common spacing issues in mathematical expressions
        text = re.sub(r'(\d+)\s*=\s*(\d+)', r'\1 = \2', text)  # Fix equals spacing
        text = re.sub(r'(\d+)\s*>\s*(\d+)', r'\1 > \2', text)  # Fix greater than spacing
        text = re.sub(r'(\d+)\s*<\s*(\d+)', r'\1 < \2', text)  # Fix less than spacing

        # Fix fraction notation
        text = re.sub(r'(\d+)\s*/\s*(\d+)', r'\1/\2', text)  # Remove spaces in simple fractions

        # Fix degree notation
        text = re.sub(r'(\d+)\s*°', r'\1°', text)  # Fix degree symbol spacing
        text = re.sub(r'(\d+)\s*degrees?', r'\1°', text)  # Convert "degrees" to symbol

        # Fix square root notation
        text = re.sub(r'√\s*(\d+)', r'√\1', text)  # Remove space after square root
        text = re.sub(r'sqrt\s*\(([^)]+)\)', r'√(\1)', text)  # Convert sqrt() to √()

        # Fix parentheses spacing
        text = re.sub(r'\(\s+', '(', text)  # Remove space after opening parenthesis
        text = re.sub(r'\s+\)', ')', text)  # Remove space before closing parenthesis

        # Fix common mathematical sequences
        text = re.sub(r'cos\s*(\d+)°', r'cos(\1°)', text)  # Fix cos30° format
        text = re.sub(r'sin\s*(\d+)°', r'sin(\1°)', text)  # Fix sin30° format
        text = re.sub(r'tan\s*(\d+)°', r'tan(\1°)', text)  # Fix tan30° format

        return text



    def convert_to_latex(self, text):
        """Convert OCR text to LaTeX format with 100% perfect mathematical recognition"""
        import re

        # Start with cleaned text
        latex_text = text.strip()

        # Step 1: Pre-processing - normalize input
        latex_text = self.preprocess_mathematical_text(latex_text)

        # Step 2: Convert symbols to LaTeX (order matters!)
        latex_text = self.convert_symbols_to_latex_perfect(latex_text)

        # Step 3: Handle mathematical structures in precise order
        latex_text = self.convert_trigonometric_functions_perfect(latex_text)
        latex_text = self.convert_fractions_perfect(latex_text)
        latex_text = self.convert_square_roots_perfect(latex_text)
        latex_text = self.convert_superscripts_subscripts_perfect(latex_text)
        latex_text = self.convert_mathematical_relations_perfect(latex_text)

        # Step 4: Final professional cleanup
        latex_text = self.final_latex_cleanup_perfect(latex_text)

        return latex_text

    def preprocess_mathematical_text(self, text):
        """Pre-process mathematical text for perfect LaTeX conversion"""
        import re

        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text).strip()

        # Normalize common OCR errors
        text = text.replace('|', '1')  # Common OCR error
        text = text.replace('O', '0')  # In mathematical contexts

        # Ensure proper spacing around key operators
        text = re.sub(r'([=><≤≥≠])', r' \1 ', text)
        text = re.sub(r'([+\-])', r' \1 ', text)

        # Clean up multiple spaces
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def convert_symbols_to_latex_perfect(self, text):
        """Convert mathematical symbols to LaTeX with perfect precision"""
        import re

        # Greek letters (comprehensive mapping)
        greek_map = {
            'α': '\\alpha', 'β': '\\beta', 'γ': '\\gamma', 'δ': '\\delta',
            'ε': '\\varepsilon', 'ζ': '\\zeta', 'η': '\\eta', 'θ': '\\theta',
            'ι': '\\iota', 'κ': '\\kappa', 'λ': '\\lambda', 'μ': '\\mu',
            'ν': '\\nu', 'ξ': '\\xi', 'ο': 'o', 'π': '\\pi',
            'ρ': '\\rho', 'σ': '\\sigma', 'τ': '\\tau', 'υ': '\\upsilon',
            'φ': '\\varphi', 'χ': '\\chi', 'ψ': '\\psi', 'ω': '\\omega',
            'Γ': '\\Gamma', 'Δ': '\\Delta', 'Θ': '\\Theta', 'Λ': '\\Lambda',
            'Ξ': '\\Xi', 'Π': '\\Pi', 'Σ': '\\Sigma', 'Υ': '\\Upsilon',
            'Φ': '\\Phi', 'Ψ': '\\Psi', 'Ω': '\\Omega'
        }

        # Mathematical operators (precise LaTeX)
        operator_map = {
            '≤': '\\leq', '≥': '\\geq', '≠': '\\neq', '≈': '\\approx',
            '±': '\\pm', '∓': '\\mp', '×': '\\times', '÷': '\\div',
            '·': '\\cdot', '∘': '\\circ', '∞': '\\infty',
            '∫': '\\int', '∑': '\\sum', '∏': '\\prod',
            '√': '\\sqrt', '∂': '\\partial', '∇': '\\nabla',
            '∈': '\\in', '∉': '\\notin', '⊂': '\\subset', '⊃': '\\supset',
            '∪': '\\cup', '∩': '\\cap', '∅': '\\emptyset'
        }

        # Arrows and implications (professional)
        arrow_map = {
            '→': '\\to', '←': '\\leftarrow', '↔': '\\leftrightarrow',
            '⇒': '\\Rightarrow', '⇐': '\\Leftarrow', '⇔': '\\Leftrightarrow',
            '↗': '\\nearrow', '↖': '\\nwarrow', '↘': '\\searrow', '↙': '\\swarrow'
        }

        # Degree symbol (special handling)
        text = re.sub(r'(\d+)°', r'\1^{\\circ}', text)
        text = text.replace('°', '^{\\circ}')

        # Apply all symbol mappings
        for symbol, latex in {**greek_map, **operator_map, **arrow_map}.items():
            text = text.replace(symbol, latex)

        return text

    def convert_trigonometric_functions_perfect(self, text):
        """Convert trigonometric functions with perfect LaTeX formatting"""
        import re

        # List of trigonometric and mathematical functions
        functions = [
            'sin', 'cos', 'tan', 'cot', 'sec', 'csc',
            'arcsin', 'arccos', 'arctan', 'sinh', 'cosh', 'tanh',
            'log', 'ln', 'exp', 'det', 'lim', 'max', 'min',
            'sup', 'inf', 'gcd', 'lcm'
        ]

        for func in functions:
            # Pattern 1: func(argument) -> \func(argument)
            text = re.sub(rf'\b{func}\s*\(([^)]+)\)', rf'\\{func}(\1)', text)

            # Pattern 2: func30° -> \func(30^{\circ})
            text = re.sub(rf'\b{func}\s*(\d+)\^{{\\circ}}', rf'\\{func}(\1^{{\\circ}})', text)

            # Pattern 3: func x -> \func(x) for single variables
            text = re.sub(rf'\b{func}\s+([a-zA-Z])\b', rf'\\{func}(\1)', text)

        return text

    def convert_fractions_perfect(self, text):
        """Convert fractions with perfect LaTeX formatting"""
        import re

        # Pattern 1: Simple fractions like 1/2, 3/4
        text = re.sub(r'\b(\d+)/(\d+)\b', r'\\frac{\1}{\2}', text)

        # Pattern 2: Fractions in parentheses (1/2) -> \frac{1}{2}
        text = re.sub(r'\((\d+)/(\d+)\)', r'\\frac{\1}{\2}', text)

        # Pattern 3: Complex fractions with expressions
        # Handle (expression)/(expression) carefully
        def replace_complex_frac(match):
            num = match.group(1).strip()
            den = match.group(2).strip()
            return f'\\frac{{{num}}}{{{den}}}'

        # Match balanced parentheses for complex fractions
        text = re.sub(r'\(([^)]+)\)/\(([^)]+)\)', replace_complex_frac, text)

        # Pattern 4: Variable fractions like a/b, x/y
        text = re.sub(r'\b([a-zA-Z])/([a-zA-Z])\b', r'\\frac{\1}{\2}', text)

        # Pattern 5: Mixed fractions like 15/(25√3)
        text = re.sub(r'\b(\d+)/\(([^)]+)\)', r'\\frac{\1}{\2}', text)

        return text

    def convert_square_roots_perfect(self, text):
        """Convert square roots with perfect LaTeX formatting"""
        import re

        # Pattern 1: √n -> \sqrt{n}
        text = re.sub(r'\\sqrt\s*(\d+)', r'\\sqrt{\1}', text)

        # Pattern 2: √(expression) -> \sqrt{expression}
        text = re.sub(r'\\sqrt\s*\(([^)]+)\)', r'\\sqrt{\1}', text)

        # Pattern 3: Coefficient with square root: 25√3 -> 25\sqrt{3}
        text = re.sub(r'(\d+)\\sqrt\{(\d+)\}', r'\1\\sqrt{\2}', text)

        # Pattern 4: Fix any remaining unbraced square roots
        text = re.sub(r'\\sqrt\s*([a-zA-Z0-9]+)', r'\\sqrt{\1}', text)

        return text

    def convert_superscripts_subscripts_perfect(self, text):
        """Convert superscripts and subscripts with perfect LaTeX formatting"""
        import re

        # Superscripts
        # Pattern 1: x^n -> x^{n} (ensure braces)
        text = re.sub(r'\^(\d+)', r'^{\1}', text)
        text = re.sub(r'\^([a-zA-Z])', r'^{\1}', text)

        # Pattern 2: Handle degree symbols properly
        text = re.sub(r'\^\\circ', r'^{\\circ}', text)

        # Subscripts
        # Pattern 1: x_n -> x_{n} (ensure braces)
        text = re.sub(r'_(\d+)', r'_{\1}', text)
        text = re.sub(r'_([a-zA-Z])', r'_{\1}', text)

        return text

    def convert_mathematical_relations_perfect(self, text):
        """Convert mathematical relations with perfect LaTeX formatting"""
        import re

        # Handle missing multiplication symbols
        # Pattern 1: μ(expression) -> \mu \cdot (expression)
        text = re.sub(r'(\\[a-zA-Z]+|[a-zA-Z])\s*\(', r'\1 \\cdot (', text)

        # Pattern 2: 50μ -> 50 \cdot \mu
        text = re.sub(r'(\d+)\s*(\\[a-zA-Z]+)', r'\1 \\cdot \2', text)

        # Pattern 3: μR -> \mu \cdot R
        text = re.sub(r'(\\[a-zA-Z]+)\s*([A-Z])', r'\1 \\cdot \2', text)

        # Ensure proper spacing around operators
        text = re.sub(r'\s*=\s*', r' = ', text)
        text = re.sub(r'\s*>\s*', r' > ', text)
        text = re.sub(r'\s*<\s*', r' < ', text)
        text = re.sub(r'\s*\\leq\s*', r' \\leq ', text)
        text = re.sub(r'\s*\\geq\s*', r' \\geq ', text)

        # Handle implications with proper spacing
        text = re.sub(r'\s*\\Rightarrow\s*', r' \\Rightarrow ', text)

        return text

    def final_latex_cleanup_perfect(self, text):
        """Final cleanup for perfect LaTeX output"""
        import re

        # Remove extra spaces
        text = re.sub(r'\s+', ' ', text)

        # Ensure proper spacing around operators (handle each separately)
        # Mathematical operators
        text = re.sub(r'\s*\+\s*', ' + ', text)
        text = re.sub(r'\s*-\s*', ' - ', text)
        text = re.sub(r'\s*=\s*', ' = ', text)
        text = re.sub(r'\s*>\s*', ' > ', text)
        text = re.sub(r'\s*<\s*', ' < ', text)

        # LaTeX operators (escape backslashes properly)
        text = re.sub(r'\s*\\times\s*', r' \\times ', text)
        text = re.sub(r'\s*\\cdot\s*', r' \\cdot ', text)
        text = re.sub(r'\s*\\div\s*', r' \\div ', text)
        text = re.sub(r'\s*\\leq\s*', r' \\leq ', text)
        text = re.sub(r'\s*\\geq\s*', r' \\geq ', text)
        text = re.sub(r'\s*\\neq\s*', r' \\neq ', text)
        text = re.sub(r'\s*\\Rightarrow\s*', r' \\Rightarrow ', text)
        text = re.sub(r'\s*\\Leftarrow\s*', r' \\Leftarrow ', text)

        # Clean up parentheses spacing
        text = re.sub(r'\(\s+', '(', text)
        text = re.sub(r'\s+\)', ')', text)

        # Clean up braces spacing
        text = re.sub(r'\{\s+', '{', text)
        text = re.sub(r'\s+\}', '}', text)

        # Final trim
        return text.strip()

    def convert_symbols_to_latex(self, text):
        """Convert mathematical symbols to LaTeX with careful replacement"""
        import re

        # First handle trigonometric functions (before other symbol replacements)
        trig_functions = ['cos', 'sin', 'tan', 'sec', 'csc', 'cot']
        for func in trig_functions:
            # Add backslash to trigonometric functions
            text = re.sub(r'\b' + func + r'\b', '\\\\' + func, text)

        # Greek letters (most common in physics/math)
        symbol_map = {
            'μ': '\\mu', 'α': '\\alpha', 'β': '\\beta', 'γ': '\\gamma',
            'δ': '\\delta', 'ε': '\\epsilon', 'θ': '\\theta', 'λ': '\\lambda',
            'π': '\\pi', 'ρ': '\\rho', 'σ': '\\sigma', 'τ': '\\tau',
            'φ': '\\phi', 'χ': '\\chi', 'ψ': '\\psi', 'ω': '\\omega',

            # Mathematical operators
            '≤': '\\leq', '≥': '\\geq', '≠': '\\neq', '±': '\\pm',
            '×': '\\times', '÷': '\\div', '·': '\\cdot',
            '∞': '\\infty', '∫': '\\int', '∑': '\\sum', '∏': '\\prod',
            '√': '\\sqrt', '∂': '\\partial', '∇': '\\nabla',

            # Arrows and implications
            '→': '\\rightarrow', '←': '\\leftarrow', '↔': '\\leftrightarrow',
            '⇒': '\\Rightarrow', '⇐': '\\Leftarrow', '⇔': '\\Leftrightarrow',

            # Degree symbol
            '°': '^\\circ',
        }

        for symbol, latex in symbol_map.items():
            text = text.replace(symbol, latex)

        return text

    def convert_fractions_robust(self, text):
        """Convert fractions with robust pattern matching"""
        import re

        # Pattern 1: Simple fractions like 3/5, 15/25, 1/2
        text = re.sub(r'\b(\d+)/(\d+)\b', r'\\frac{\1}{\2}', text)

        # Pattern 2: Fractions in parentheses like (1/2), (3/4)
        text = re.sub(r'\((\d+)/(\d+)\)', r'\\frac{\1}{\2}', text)

        # Pattern 3: Fractions with square roots like 15/(25√3) or 15/(25\\sqrt{3})
        text = re.sub(r'\b(\d+)/\(([^)]+)\)', r'\\frac{\1}{\2}', text)

        # Pattern 4: Complex expressions like (√3/2) or (\\sqrt{3}/2)
        text = re.sub(r'\(([^)]+)/([^)]+)\)', r'\\left(\\frac{\1}{\2}\\right)', text)

        # Pattern 5: Variable fractions like μR/5g or \\mu R/5g
        text = re.sub(r'([a-zA-Z\\]+\s*[a-zA-Z]*)/([a-zA-Z\\]+\s*[a-zA-Z]*)', r'\\frac{\1}{\2}', text)

        # Pattern 6: Fractions with expressions like 3√3/15 or 3\\sqrt{3}/15
        text = re.sub(r'(\d+\\sqrt\{\d+\})/(\d+)', r'\\frac{\1}{\2}', text)
        text = re.sub(r'(\d+\s*\\sqrt\{\d+\})/(\d+)', r'\\frac{\1}{\2}', text)

        # Pattern 7: Handle remaining slash patterns that look like fractions
        text = re.sub(r'([a-zA-Z]\d*)/(\d+)', r'\\frac{\1}{\2}', text)  # like l/5 -> \frac{l}{5}

        return text

    def convert_square_roots_robust(self, text):
        """Convert square root expressions with robust handling"""
        import re

        # Pattern 1: Simple square roots like √3, √25
        text = re.sub(r'\\sqrt\s*(\d+)', r'\\sqrt{\1}', text)

        # Pattern 2: Square roots with parentheses like √(expression)
        text = re.sub(r'\\sqrt\s*\(([^)]+)\)', r'\\sqrt{\1}', text)

        # Pattern 3: Coefficients with square roots like 25√3
        text = re.sub(r'(\d+)\\sqrt\{(\d+)\}', r'\1\\sqrt{\2}', text)

        # Pattern 4: Fix any remaining unbraced square roots
        text = re.sub(r'\\sqrt\s*([a-zA-Z0-9]+)', r'\\sqrt{\1}', text)

        return text

    def convert_trigonometric_functions_robust(self, text):
        """Convert trigonometric functions with proper formatting"""
        import re

        # Pattern 1: Functions with degree arguments like cos(30°)
        text = re.sub(r'\\(cos|sin|tan)\((\d+)\^\\circ\)', r'\\\1(\2^\\circ)', text)

        # Pattern 2: Functions without parentheses like cos30°
        text = re.sub(r'\\(cos|sin|tan)\s*(\d+)\^\\circ', r'\\\1(\2^\\circ)', text)

        # Pattern 3: Functions with simple arguments
        text = re.sub(r'\\(cos|sin|tan)\s*(\d+)', r'\\\1(\2^\\circ)', text)

        return text

    def convert_superscripts_subscripts_robust(self, text):
        """Convert superscripts and subscripts with robust handling"""
        import re

        # Pattern 1: Degree symbols as superscripts
        text = re.sub(r'(\d+)\^\\circ', r'\1^{\\circ}', text)

        # Pattern 2: Simple superscripts
        text = re.sub(r'\^(\d+)', r'^{\1}', text)
        text = re.sub(r'\^([a-zA-Z])', r'^{\1}', text)

        # Pattern 3: Simple subscripts
        text = re.sub(r'_(\d+)', r'_{\1}', text)
        text = re.sub(r'_([a-zA-Z])', r'_{\1}', text)

        return text

    def convert_mathematical_relations_robust(self, text):
        """Convert mathematical relations and equations"""
        import re

        # Handle missing multiplication symbols between variables and parentheses
        # Pattern: μ(expression) -> μ \times (expression)
        text = re.sub(r'(\\mu|\\alpha|\\beta|\\gamma|\\delta|\\theta|\\pi|\\sigma|\\phi|\\omega|[a-zA-Z])\s*\(', r'\1 \\times (', text)

        # Handle missing multiplication symbols between numbers and variables
        # Pattern: 50μ -> 50 \times μ, 25√3 -> 25√3 (keep as is for square roots)
        text = re.sub(r'(\d+)\s*(\\mu|\\alpha|\\beta|\\gamma|\\delta|\\theta|\\pi|\\sigma|\\phi|\\omega)(?![a-zA-Z])', r'\1 \\times \2', text)

        # Handle missing multiplication symbols between variables
        # Pattern: μR -> μ \times R (but be careful not to break existing LaTeX commands)
        text = re.sub(r'(\\mu|\\alpha|\\beta|\\gamma|\\delta|\\theta|\\pi|\\sigma|\\phi|\\omega)\s*([A-Z][a-zA-Z]*)', r'\1 \\times \2', text)

        # Ensure proper spacing around operators
        text = re.sub(r'(\w+)\s*=\s*(\w+)', r'\1 = \2', text)
        text = re.sub(r'(\w+)\s*>\s*(\w+)', r'\1 > \2', text)
        text = re.sub(r'(\w+)\s*<\s*(\w+)', r'\1 < \2', text)

        # Handle implications
        text = re.sub(r'\\Rightarrow\s*', r' \\Rightarrow ', text)

        return text

    def final_latex_cleanup(self, text):
        """Final cleanup of LaTeX text"""
        import re

        # Remove extra spaces
        text = re.sub(r'\s+', ' ', text)

        # Fix spacing around operators
        text = re.sub(r'\s*\\times\s*', r' \\times ', text)
        text = re.sub(r'\s*\\Rightarrow\s*', r' \\Rightarrow ', text)

        # Clean up parentheses
        text = re.sub(r'\(\s+', '(', text)
        text = re.sub(r'\s+\)', ')', text)

        return text.strip()

    def convert_complex_fractions(self, text):
        """Convert complex fraction patterns to LaTeX"""
        # Handle nested fractions and complex expressions

        # Pattern 1: (expression/expression) - parenthetical fractions
        text = re.sub(r'\(([^)]+)/([^)]+)\)', r'\\frac{\1}{\2}', text)

        # Pattern 2: Simple number fractions
        text = re.sub(r'(\d+(?:\.\d+)?)/(\d+(?:\.\d+)?)', r'\\frac{\1}{\2}', text)

        # Pattern 3: Variable/number fractions
        text = re.sub(r'([a-zA-Z√]+)/(\d+)', r'\\frac{\1}{\2}', text)

        # Pattern 4: Complex expressions like "25√3/5√3"
        text = re.sub(r'(\d+√\d+)/(\d+√\d+)', r'\\frac{\1}{\2}', text)

        # Pattern 5: Expressions with Greek letters like "15/25√3"
        text = re.sub(r'(\d+)/(\d+\\sqrt\{\d+\})', r'\\frac{\1}{\2}', text)

        # Pattern 6: Handle "R = 5g cos30°" type expressions
        text = re.sub(r'(\w+)\s*=\s*(\d+\w*)\s*(\\cos|\\sin|\\tan)\s*(\d+°)', r'\1 = \2 \3(\4)', text)

        return text

    def convert_superscripts_subscripts(self, text):
        """Convert superscripts and subscripts with enhanced pattern matching"""
        # Handle degree symbols as superscripts
        text = re.sub(r'(\d+)°', r'\1^\\circ', text)

        # Handle regular superscripts
        text = re.sub(r'\^(\d+)', r'^{\1}', text)
        text = re.sub(r'\^([a-zA-Z])', r'^{\1}', text)
        text = re.sub(r'\^(\([^)]+\))', r'^{\1}', text)  # Handle ^(expression)

        # Handle subscripts
        text = re.sub(r'_(\d+)', r'_{\1}', text)
        text = re.sub(r'_([a-zA-Z])', r'_{\1}', text)

        # Handle combined super/subscripts
        text = re.sub(r'([a-zA-Z])(\d+)', r'\1_{\2}', text)  # Like R50 -> R_{50}

        return text

    def convert_square_roots(self, text):
        """Convert square root expressions with proper LaTeX formatting"""
        # Handle √3, √25, etc.
        text = re.sub(r'√(\d+)', r'\\sqrt{\1}', text)

        # Handle √(expression)
        text = re.sub(r'√\(([^)]+)\)', r'\\sqrt{\1}', text)

        # Handle complex expressions like "25√3"
        text = re.sub(r'(\d+)√(\d+)', r'\1\\sqrt{\2}', text)

        # Handle expressions like "√3/√3"
        text = re.sub(r'\\sqrt\s*([^{}\s]+)', r'\\sqrt{\1}', text)

        return text

    def convert_trig_functions(self, text):
        """Convert trigonometric functions with proper argument formatting"""
        # Handle cos30°, sin30°, etc.
        text = re.sub(r'\\(cos|sin|tan)\s*(\d+)°', r'\\\1(\2^\\circ)', text)
        text = re.sub(r'\\(cos|sin|tan)\s*(\d+)', r'\\\1(\2^\\circ)', text)

        # Handle cos(30°), sin(30°), etc.
        text = re.sub(r'\\(cos|sin|tan)\((\d+)°\)', r'\\\1(\2^\\circ)', text)

        return text

    def convert_mathematical_relations(self, text):
        """Convert mathematical relations and inequalities"""
        # Handle equations with proper spacing
        text = re.sub(r'(\w+)\s*=\s*(\w+)', r'\1 = \2', text)

        # Handle inequalities
        text = re.sub(r'(\d+)\s*>\s*([^>]+)', r'\1 > \2', text)
        text = re.sub(r'(\d+)\s*<\s*([^<]+)', r'\1 < \2', text)

        # Handle "⇒" implications
        text = re.sub(r'\s*⇒\s*', r' \\Rightarrow ', text)
        text = re.sub(r'\s*=>\s*', r' \\Rightarrow ', text)

        # Handle multiplication with proper spacing
        text = re.sub(r'(\d+)\s*×\s*(\d+)', r'\1 \\times \2', text)

        return text

    def add_to_queue(self):
        """Add current equation to the queue"""
        if not hasattr(self, 'current_selection') or not self.current_images:
            messagebox.showwarning("Warning", "No equation to add.")
            return

        latex_text = self.latex_text.get(1.0, tk.END).strip()
        if not latex_text:
            messagebox.showwarning("Warning", "Please enter LaTeX text.")
            return

        img_data = self.current_images[self.current_page]
        sel = self.current_selection

        equation = EquationRegion(
            x=sel['x'], y=sel['y'],
            width=sel['width'], height=sel['height'],
            page_num=img_data['page_num'],
            filename=img_data['filename'],
            latex_text=latex_text,
            confidence=getattr(self, 'current_confidence', 0)
        )

        self.equation_queue.append(equation)
        self.update_queue_display()

        # Clear current selection
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)
            self.selection_rect = None
        delattr(self, 'current_selection')

    def update_queue_display(self):
        """Update the equation queue display"""
        # Clear existing items
        for item in self.queue_tree.get_children():
            self.queue_tree.delete(item)

        # Add equations to tree
        for i, eq in enumerate(self.equation_queue):
            self.queue_tree.insert('', 'end', text=str(i+1), values=(
                f"Page {eq.page_num}",
                eq.latex_text[:50] + "..." if len(eq.latex_text) > 50 else eq.latex_text,
                f"{eq.confidence:.1f}%"
            ))

    def export_to_word(self):
        """Export equations to Word document"""
        if not self.equation_queue:
            messagebox.showwarning("Warning", "No equations to export.")
            return

        filename = filedialog.asksaveasfilename(
            title="Save Word Document",
            defaultextension=".docx",
            filetypes=[("Word documents", "*.docx"), ("All files", "*.*")]
        )

        if filename:
            try:
                self.create_word_document(filename)
                messagebox.showinfo("Success", f"Document saved as {filename}")
            except Exception as e:
                messagebox.showerror("Export Error", str(e))

    def create_word_document(self, filename):
        """Create Word document with equations using safe Unicode formatting"""
        from docx import Document
        from docx.shared import Pt

        doc = Document()
        doc.add_heading('Mathematical Equations', 0)

        for i, eq in enumerate(self.equation_queue):
            # Add equation number and source
            if self.settings['show_page_refs']:
                doc.add_paragraph(f"Equation {i+1} (from {eq.filename}):")
            else:
                doc.add_paragraph(f"Equation {i+1}:")

            # Use safe Unicode conversion instead of complex OMML
            try:
                # Convert LaTeX to readable Unicode
                unicode_text = self.latex_to_unicode_safe(eq.latex_text)

                # Create equation paragraph with proper formatting
                p = doc.add_paragraph()
                run = p.add_run(unicode_text)

                # Format as mathematical equation
                run.font.name = 'Cambria Math'
                run.font.size = Pt(14)

                # Center the equation
                p.alignment = 1  # Center alignment

            except Exception as e:
                print(f"Equation conversion error: {e}")
                # Ultimate fallback to plain text
                doc.add_paragraph(eq.latex_text)

            doc.add_paragraph()  # Add spacing

        doc.save(filename)

    def latex_to_unicode_safe(self, latex_text):
        """Convert LaTeX to Unicode with proper fraction and mathematical formatting"""
        import re

        # Start with a copy of the input
        result = latex_text

        # Handle fractions more intelligently
        # Convert \frac{a}{b} to (a)/(b) with proper Unicode formatting
        def replace_frac(match):
            num = match.group(1)
            den = match.group(2)
            # Clean up the numerator and denominator recursively
            num_clean = self.latex_to_unicode(num)
            den_clean = self.latex_to_unicode(den)
            return f"({num_clean})/({den_clean})"

        # Handle fractions first
        result = re.sub(r'\\frac\{([^}]+)\}\{([^}]+)\}', replace_frac, result)
        result = re.sub(r'\\left\(\\frac\{([^}]+)\}\{([^}]+)\}\\right\)',
                       lambda m: f"({replace_frac(m)})", result)

        # Handle square roots
        result = re.sub(r'\\sqrt\{([^}]+)\}', r'√(\1)', result)

        # Handle superscripts (simple cases)
        result = re.sub(r'\^\\circ', '°', result)
        result = re.sub(r'\^\{\\circ\}', '°', result)

        # Now apply the basic unicode conversion
        result = self.latex_to_unicode(result)

        # Clean up remaining LaTeX artifacts
        result = result.replace('\\left(', '(').replace('\\right)', ')')
        result = result.replace('\\left', '').replace('\\right', '')

        # Clean up extra braces more carefully
        result = re.sub(r'\{([^}]*)\}', r'\1', result)

        # Fix spacing
        result = re.sub(r'\s+', ' ', result)
        result = result.strip()

        return result

    def insert_simple_equation(self, paragraph, latex_text):
        """Insert a simple equation when OMML fails"""
        # Convert LaTeX symbols to Unicode for better display
        unicode_text = self.latex_to_unicode(latex_text)
        run = paragraph.add_run(unicode_text)
        # Make it look more like an equation
        run.font.name = 'Cambria Math'
        run.font.size = 12

    def latex_to_unicode(self, latex_text):
        """Convert LaTeX to Unicode symbols for fallback display"""
        symbol_map = {
            '\\mu': 'μ', '\\alpha': 'α', '\\beta': 'β', '\\gamma': 'γ',
            '\\delta': 'δ', '\\theta': 'θ', '\\pi': 'π', '\\sigma': 'σ',
            '\\phi': 'φ', '\\omega': 'ω', '\\times': '×', '\\div': '÷',
            '\\pm': '±', '\\leq': '≤', '\\geq': '≥', '\\neq': '≠',
            '\\Rightarrow': '⇒', '\\rightarrow': '→', '\\leftarrow': '←',
            '\\infty': '∞', '\\int': '∫', '\\sum': '∑', '^\\circ': '°',
            '\\sqrt': '√', '\\frac': '', '\\left': '', '\\right': '',
            '{': '', '}': '', '\\cos': 'cos', '\\sin': 'sin', '\\tan': 'tan'
        }

        result = latex_text
        for latex_sym, unicode_sym in symbol_map.items():
            result = result.replace(latex_sym, unicode_sym)

        return result

    def latex_to_omml(self, latex_text):
        """Convert LaTeX to OMML XML with proper mathematical formatting"""
        try:
            # Clean up the LaTeX text
            latex_text = latex_text.strip()
            if not latex_text:
                return None

            # Remove common LaTeX delimiters but preserve structure
            latex_text = latex_text.replace('$', '').replace('\\[', '').replace('\\]', '')
            latex_text = latex_text.replace('\\(', '').replace('\\)', '')

            # Handle multi-line equations (remove \begin{aligned} etc.)
            latex_text = latex_text.replace('\\begin{aligned}', '').replace('\\end{aligned}', '')
            latex_text = latex_text.replace('\\\\', ' ')  # Replace line breaks with spaces
            latex_text = latex_text.replace('&', '')  # Remove alignment markers

            # Convert common LaTeX patterns to OMML
            omml_content = self.convert_latex_to_omml_robust(latex_text)

            # Wrap in proper OMML structure with namespace
            omml_xml = f'''<m:oMath xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math">
                <m:oMathPara>
                    <m:oMathParaPr>
                        <m:jc m:val="left"/>
                    </m:oMathParaPr>
                    {omml_content}
                </m:oMathPara>
            </m:oMath>'''

            return omml_xml

        except Exception as e:
            print(f"LaTeX to OMML conversion error: {e}")
            # Return a simple fallback
            return f'''<m:oMath xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math">
                <m:r><m:t>{latex_text}</m:t></m:r>
            </m:oMath>'''

    def convert_latex_to_omml_robust(self, latex_text):
        """Robust LaTeX to OMML conversion with better pattern handling"""
        import re

        # Handle fractions first (most complex)
        latex_text = self.convert_fractions_to_omml(latex_text)

        # Handle square roots
        latex_text = self.convert_sqrt_to_omml(latex_text)

        # Handle superscripts and subscripts
        latex_text = self.convert_scripts_to_omml(latex_text)

        # Handle mathematical functions
        latex_text = self.convert_functions_to_omml(latex_text)

        # Handle symbols
        latex_text = self.convert_symbols_to_omml(latex_text)

        # Wrap remaining text
        if not latex_text.startswith('<m:'):
            latex_text = f'<m:r><m:t>{latex_text}</m:t></m:r>'

        return latex_text

    def convert_fractions_to_omml(self, text):
        """Convert LaTeX fractions to OMML"""
        import re

        # Pattern for \frac{numerator}{denominator}
        def replace_frac(match):
            num = match.group(1)
            den = match.group(2)
            return f'''<m:f>
                <m:fPr></m:fPr>
                <m:num><m:r><m:t>{num}</m:t></m:r></m:num>
                <m:den><m:r><m:t>{den}</m:t></m:r></m:den>
            </m:f>'''

        text = re.sub(r'\\frac\{([^}]+)\}\{([^}]+)\}', replace_frac, text)
        text = re.sub(r'\\left\(\\frac\{([^}]+)\}\{([^}]+)\}\\right\)',
                     lambda m: f'<m:d><m:dPr></m:dPr><m:e>{replace_frac(m)}</m:e></m:d>', text)

        return text

    def convert_sqrt_to_omml(self, text):
        """Convert LaTeX square roots to OMML"""
        import re

        def replace_sqrt(match):
            content = match.group(1)
            return f'''<m:rad>
                <m:radPr></m:radPr>
                <m:deg></m:deg>
                <m:e><m:r><m:t>{content}</m:t></m:r></m:e>
            </m:rad>'''

        text = re.sub(r'\\sqrt\{([^}]+)\}', replace_sqrt, text)
        return text

    def convert_scripts_to_omml(self, text):
        """Convert superscripts and subscripts to OMML"""
        import re

        # Superscripts
        def replace_sup(match):
            base = match.group(1)
            sup = match.group(2)
            return f'''<m:sSup>
                <m:sSupPr></m:sSupPr>
                <m:e><m:r><m:t>{base}</m:t></m:r></m:e>
                <m:sup><m:r><m:t>{sup}</m:t></m:r></m:sup>
            </m:sSup>'''

        text = re.sub(r'([^\\]+)\^\{([^}]+)\}', replace_sup, text)

        # Subscripts
        def replace_sub(match):
            base = match.group(1)
            sub = match.group(2)
            return f'''<m:sSub>
                <m:sSubPr></m:sSubPr>
                <m:e><m:r><m:t>{base}</m:t></m:r></m:e>
                <m:sub><m:r><m:t>{sub}</m:t></m:r></m:sub>
            </m:sSub>'''

        text = re.sub(r'([^\\]+)_\{([^}]+)\}', replace_sub, text)

        return text

    def convert_functions_to_omml(self, text):
        """Convert mathematical functions to OMML"""
        import re

        # Trigonometric functions
        functions = ['cos', 'sin', 'tan', 'sec', 'csc', 'cot', 'log', 'ln']
        for func in functions:
            pattern = f'\\\\{func}'
            replacement = f'<m:func><m:funcPr></m:funcPr><m:fName><m:r><m:t>{func}</m:t></m:r></m:fName><m:e><m:r><m:t></m:t></m:r></m:e></m:func>'
            text = text.replace(pattern, replacement)

        return text

    def convert_symbols_to_omml(self, text):
        """Convert mathematical symbols to OMML"""
        symbol_map = {
            '\\mu': 'μ',
            '\\alpha': 'α',
            '\\beta': 'β',
            '\\gamma': 'γ',
            '\\delta': 'δ',
            '\\theta': 'θ',
            '\\pi': 'π',
            '\\sigma': 'σ',
            '\\phi': 'φ',
            '\\omega': 'ω',
            '\\times': '×',
            '\\div': '÷',
            '\\pm': '±',
            '\\leq': '≤',
            '\\geq': '≥',
            '\\neq': '≠',
            '\\Rightarrow': '⇒',
            '\\rightarrow': '→',
            '\\leftarrow': '←',
            '\\infty': '∞',
            '\\int': '∫',
            '\\sum': '∑',
            '^\\circ': '°'
        }

        for latex_sym, unicode_sym in symbol_map.items():
            text = text.replace(latex_sym, unicode_sym)

        return text

    def convert_latex_patterns(self, latex_text):
        """Convert LaTeX patterns to OMML elements with enhanced support for complex expressions"""
        # Handle multiple patterns in order of complexity

        # First handle fractions (most complex)
        if '\\frac' in latex_text:
            return self.convert_fraction(latex_text)

        # Handle square roots with potential nested expressions
        elif '\\sqrt' in latex_text:
            return self.convert_sqrt(latex_text)

        # Handle superscripts (including degree symbols)
        elif '^' in latex_text:
            return self.convert_superscript(latex_text)

        # Handle subscripts
        elif '_' in latex_text:
            return self.convert_subscript(latex_text)

        # Handle trigonometric functions
        elif any(func in latex_text for func in ['\\cos', '\\sin', '\\tan']):
            return self.convert_trig_function_omml(latex_text)

        # Handle mathematical relations and implications
        elif '\\Rightarrow' in latex_text or '\\times' in latex_text:
            return self.convert_mathematical_expression(latex_text)

        # Handle simple text with mathematical symbols
        else:
            return self.convert_simple_math(latex_text)

    def convert_trig_function_omml(self, latex_text):
        """Convert trigonometric functions to OMML"""
        import re

        # Pattern for trig functions like \cos(30^\circ)
        trig_pattern = r'\\(cos|sin|tan)\(([^)]+)\)'
        match = re.search(trig_pattern, latex_text)

        if match:
            func_name = match.group(1)
            argument = match.group(2)

            before = latex_text[:match.start()]
            after = latex_text[match.end():]

            omml_trig = f'''<m:func>
                <m:funcPr></m:funcPr>
                <m:fName><m:r><m:t>{func_name}</m:t></m:r></m:fName>
                <m:e><m:r><m:t>({argument})</m:t></m:r></m:e>
            </m:func>'''

            result = ""
            if before.strip():
                result += f'<m:r><m:t>{before}</m:t></m:r>'
            result += omml_trig
            if after.strip():
                result += self.convert_latex_patterns(after)

            return result

        return f'<m:r><m:t>{latex_text}</m:t></m:r>'

    def convert_mathematical_expression(self, latex_text):
        """Convert complex mathematical expressions with multiple elements"""
        import re

        # Split by mathematical operators while preserving them
        parts = re.split(r'(\s*\\Rightarrow\s*|\s*\\times\s*|\s*=\s*|\s*>\s*|\s*<\s*)', latex_text)

        result = ""
        for part in parts:
            part = part.strip()
            if not part:
                continue

            if part in ['\\Rightarrow', '\\times', '=', '>', '<']:
                # Convert operators to proper symbols
                symbol_map = {
                    '\\Rightarrow': '⇒',
                    '\\times': '×',
                    '=': '=',
                    '>': '>',
                    '<': '<'
                }
                result += f'<m:r><m:t> {symbol_map.get(part, part)} </m:t></m:r>'
            else:
                # Recursively process each part
                if any(pattern in part for pattern in ['\\frac', '^', '_', '\\sqrt']):
                    result += self.convert_latex_patterns(part)
                else:
                    result += f'<m:r><m:t>{part}</m:t></m:r>'

        return result

    def convert_fraction(self, latex_text):
        """Convert \\frac{numerator}{denominator} to OMML"""
        import re

        # Find fraction pattern
        frac_pattern = r'\\frac\{([^}]+)\}\{([^}]+)\}'
        match = re.search(frac_pattern, latex_text)

        if match:
            numerator = match.group(1)
            denominator = match.group(2)

            # Replace the fraction with OMML
            before = latex_text[:match.start()]
            after = latex_text[match.end():]

            omml_frac = f'''<m:f>
                <m:num><m:r><m:t>{numerator}</m:t></m:r></m:num>
                <m:den><m:r><m:t>{denominator}</m:t></m:r></m:den>
            </m:f>'''

            # Recursively handle before and after parts
            result = ""
            if before.strip():
                result += f'<m:r><m:t>{before}</m:t></m:r>'
            result += omml_frac
            if after.strip():
                result += self.convert_latex_patterns(after)

            return result

        return f'<m:r><m:t>{latex_text}</m:t></m:r>'

    def convert_superscript(self, latex_text):
        """Convert x^{power} to OMML superscript"""
        import re

        # Find superscript pattern
        sup_pattern = r'([^_^]+)\^\{([^}]+)\}'
        match = re.search(sup_pattern, latex_text)

        if match:
            base = match.group(1)
            power = match.group(2)

            before = latex_text[:match.start()]
            after = latex_text[match.end():]

            omml_sup = f'''<m:sSup>
                <m:e><m:r><m:t>{base}</m:t></m:r></m:e>
                <m:sup><m:r><m:t>{power}</m:t></m:r></m:sup>
            </m:sSup>'''

            result = ""
            if before.strip():
                result += f'<m:r><m:t>{before}</m:t></m:r>'
            result += omml_sup
            if after.strip():
                result += self.convert_latex_patterns(after)

            return result

        return f'<m:r><m:t>{latex_text}</m:t></m:r>'

    def convert_subscript(self, latex_text):
        """Convert x_{subscript} to OMML subscript"""
        import re

        # Find subscript pattern
        sub_pattern = r'([^_^]+)_\{([^}]+)\}'
        match = re.search(sub_pattern, latex_text)

        if match:
            base = match.group(1)
            subscript = match.group(2)

            before = latex_text[:match.start()]
            after = latex_text[match.end():]

            omml_sub = f'''<m:sSub>
                <m:e><m:r><m:t>{base}</m:t></m:r></m:e>
                <m:sub><m:r><m:t>{subscript}</m:t></m:r></m:sub>
            </m:sSub>'''

            result = ""
            if before.strip():
                result += f'<m:r><m:t>{before}</m:t></m:r>'
            result += omml_sub
            if after.strip():
                result += self.convert_latex_patterns(after)

            return result

        return f'<m:r><m:t>{latex_text}</m:t></m:r>'

    def convert_sqrt(self, latex_text):
        """Convert \\sqrt{expression} to OMML square root"""
        import re

        # Find square root pattern
        sqrt_pattern = r'\\sqrt\{([^}]+)\}'
        match = re.search(sqrt_pattern, latex_text)

        if match:
            expression = match.group(1)

            before = latex_text[:match.start()]
            after = latex_text[match.end():]

            omml_sqrt = f'''<m:rad>
                <m:deg></m:deg>
                <m:e><m:r><m:t>{expression}</m:t></m:r></m:e>
            </m:rad>'''

            result = ""
            if before.strip():
                result += f'<m:r><m:t>{before}</m:t></m:r>'
            result += omml_sqrt
            if after.strip():
                result += self.convert_latex_patterns(after)

            return result

        return f'<m:r><m:t>{latex_text}</m:t></m:r>'

    def convert_simple_math(self, latex_text):
        """Convert simple mathematical text to OMML"""
        # Replace common mathematical symbols
        replacements = {
            '\\alpha': 'α', '\\beta': 'β', '\\gamma': 'γ', '\\delta': 'δ',
            '\\epsilon': 'ε', '\\theta': 'θ', '\\lambda': 'λ', '\\mu': 'μ',
            '\\pi': 'π', '\\sigma': 'σ', '\\phi': 'φ', '\\omega': 'ω',
            '\\infty': '∞', '\\pm': '±', '\\mp': '∓',
            '\\leq': '≤', '\\geq': '≥', '\\neq': '≠',
            '\\approx': '≈', '\\equiv': '≡',
            '\\cdot': '·', '\\times': '×', '\\div': '÷',
            '\\sum': '∑', '\\prod': '∏', '\\int': '∫',
            '\\partial': '∂', '\\nabla': '∇',
            '\\rightarrow': '→', '\\leftarrow': '←', '\\leftrightarrow': '↔',
            '\\Rightarrow': '⇒', '\\Leftarrow': '⇐', '\\Leftrightarrow': '⇔'
        }

        for latex_symbol, unicode_symbol in replacements.items():
            latex_text = latex_text.replace(latex_symbol, unicode_symbol)

        # Remove remaining backslashes from unknown commands
        latex_text = re.sub(r'\\[a-zA-Z]+', '', latex_text)

        return f'<m:r><m:t>{latex_text}</m:t></m:r>'

    # Additional methods for navigation, settings, etc.
    def prev_page(self):
        if self.current_page > 0:
            self.current_page -= 1
            self.display_current_image()

    def next_page(self):
        if self.current_page < len(self.current_images) - 1:
            self.current_page += 1
            self.display_current_image()

    def on_file_select(self, event):
        if hasattr(self, 'file_listbox'):
            selection = self.file_listbox.curselection()
            if selection:
                self.current_page = selection[0]
                self.display_current_image()

    def zoom_in(self):
        """Zoom in on the current image"""
        if self.current_images:
            self.zoom_factor = min(self.zoom_factor * 1.25, 5.0)  # Max 5x zoom
            self.display_current_image()

    def zoom_out(self):
        """Zoom out on the current image"""
        if self.current_images:
            self.zoom_factor = max(self.zoom_factor / 1.25, 0.1)  # Min 0.1x zoom
            self.display_current_image()

    def fit_to_window(self):
        """Fit image to window size"""
        if not self.current_images or not self.original_image:
            return

        # Get canvas dimensions
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        # Get image dimensions
        img_width = self.original_image.width
        img_height = self.original_image.height

        # Calculate zoom factor to fit image in canvas
        if canvas_width > 1 and canvas_height > 1:  # Ensure canvas is initialized
            zoom_x = (canvas_width - 20) / img_width  # 20px padding
            zoom_y = (canvas_height - 20) / img_height  # 20px padding
            self.zoom_factor = min(zoom_x, zoom_y, 1.0)  # Don't zoom in beyond 100%
            self.display_current_image()

    def on_mouse_wheel(self, event):
        """Handle mouse wheel zoom"""
        if self.current_images:
            # Check if Ctrl is pressed for zoom
            if event.state & 0x4:  # Ctrl key
                if event.delta > 0:
                    self.zoom_in()
                else:
                    self.zoom_out()
            else:
                # Normal scroll behavior for canvas
                if event.delta > 0:
                    self.canvas.yview_scroll(-1, "units")
                else:
                    self.canvas.yview_scroll(1, "units")

    def remove_from_queue(self):
        selection = self.queue_tree.selection()
        if selection:
            item = selection[0]
            index = self.queue_tree.index(item)
            del self.equation_queue[index]
            self.update_queue_display()

    def clear_queue(self):
        self.equation_queue.clear()
        self.update_queue_display()

    def move_up_queue(self):
        # Implement queue reordering
        pass

    def move_down_queue(self):
        # Implement queue reordering
        pass

    def copy_latex_from_queue(self, event):
        """Copy LaTeX text from selected queue item"""
        selection = self.queue_tree.selection()
        if selection:
            item = selection[0]
            index = self.queue_tree.index(item)
            if index < len(self.equation_queue):
                latex_text = self.equation_queue[index].latex_text
                self.root.clipboard_clear()
                self.root.clipboard_append(latex_text)
                print(f"🔥 DEBUG: Copied LaTeX to clipboard: {latex_text}")
                messagebox.showinfo("Copied", f"LaTeX copied to clipboard:\n{latex_text[:100]}...")

    def show_queue_context_menu(self, event):
        """Show context menu for queue items"""
        selection = self.queue_tree.selection()
        if selection:
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label="Copy LaTeX",
                                   command=lambda: self.copy_latex_from_queue(event))
            context_menu.add_command(label="Edit LaTeX",
                                   command=self.edit_selected_equation)
            context_menu.add_separator()
            context_menu.add_command(label="Remove",
                                   command=self.remove_from_queue)

            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

    def edit_selected_equation(self):
        """Edit the selected equation in the LaTeX editor"""
        selection = self.queue_tree.selection()
        if selection:
            item = selection[0]
            index = self.queue_tree.index(item)
            if index < len(self.equation_queue):
                equation = self.equation_queue[index]
                # Load the equation into the editor
                self.latex_text.delete(1.0, tk.END)
                self.latex_text.insert(1.0, equation.latex_text)
                print(f"🔥 DEBUG: Loaded equation for editing: {equation.latex_text}")

    def copy_selected_latex(self):
        """Copy LaTeX from selected queue item using button"""
        selection = self.queue_tree.selection()
        if selection:
            item = selection[0]
            index = self.queue_tree.index(item)
            if index < len(self.equation_queue):
                latex_text = self.equation_queue[index].latex_text
                self.root.clipboard_clear()
                self.root.clipboard_append(latex_text)
                print(f"🔥 DEBUG: Copied LaTeX to clipboard: {latex_text}")
                messagebox.showinfo("Copied", f"LaTeX copied to clipboard:\n{latex_text[:100]}...")
        else:
            messagebox.showwarning("No Selection", "Please select an equation from the queue first.")

    def open_settings(self):
        """Open comprehensive settings dialog with AI options"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("LaTeX Extractor by Yark Settings")
        settings_window.geometry("600x700")
        settings_window.resizable(True, True)

        # Make it modal
        settings_window.transient(self.root)
        settings_window.grab_set()

        # Center the window
        settings_window.update_idletasks()
        x = (settings_window.winfo_screenwidth() // 2) - (600 // 2)
        y = (settings_window.winfo_screenheight() // 2) - (700 // 2)
        settings_window.geometry(f"600x700+{x}+{y}")

        # Create notebook for tabs
        notebook = ttk.Notebook(settings_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # AI Settings Tab
        ai_frame = ttk.Frame(notebook)
        notebook.add(ai_frame, text="AI Settings")
        self._create_ai_settings_tab(ai_frame)

        # General Settings Tab
        general_frame = ttk.Frame(notebook)
        notebook.add(general_frame, text="General")
        self._create_general_settings_tab(general_frame)

        # Export Settings Tab
        export_frame = ttk.Frame(notebook)
        notebook.add(export_frame, text="Export")
        self._create_export_settings_tab(export_frame)

        # Buttons frame
        button_frame = ttk.Frame(settings_window)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Button(button_frame, text="Save",
                  command=lambda: self._save_settings(settings_window)).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Cancel",
                  command=settings_window.destroy).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="Reset to Defaults",
                  command=self._reset_settings).pack(side=tk.LEFT)

    def _create_ai_settings_tab(self, parent):
        """Create AI settings tab with comprehensive options"""
        # Create scrollable frame
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Store variables for settings
        self.ai_vars = {}

        # Master AI Toggle
        master_frame = ttk.LabelFrame(scrollable_frame, text="Master AI Control", padding=10)
        master_frame.pack(fill=tk.X, padx=10, pady=5)

        self.ai_vars['ai_enabled'] = tk.BooleanVar(value=self.settings.get('ai_enabled', False))
        master_check = ttk.Checkbutton(master_frame, text="Enable AI Processing",
                                     variable=self.ai_vars['ai_enabled'],
                                     command=self._on_master_ai_toggle)
        master_check.pack(anchor=tk.W)

        ttk.Label(master_frame, text="When disabled, all AI features are turned off for faster response times.\nOnly basic rule-based processing will be used.",
                 foreground="gray").pack(anchor=tk.W, pady=(5, 0))

        # Vision AI Settings
        vision_frame = ttk.LabelFrame(scrollable_frame, text="Vision AI (OCR Enhancement)", padding=10)
        vision_frame.pack(fill=tk.X, padx=10, pady=5)

        self.ai_vars['ai_vision_enabled'] = tk.BooleanVar(value=self.settings.get('ai_vision_enabled', False))
        vision_check = ttk.Checkbutton(vision_frame, text="Enable Qwen-VL Vision AI for OCR",
                                     variable=self.ai_vars['ai_vision_enabled'])
        vision_check.pack(anchor=tk.W)

        ttk.Label(vision_frame, text="Uses advanced vision AI to improve mathematical equation recognition.\nFalls back to LaTeX-OCR when disabled.",
                 foreground="gray").pack(anchor=tk.W, pady=(5, 0))

        # Text Enhancement AI Settings
        text_frame = ttk.LabelFrame(scrollable_frame, text="Text Enhancement AI", padding=10)
        text_frame.pack(fill=tk.X, padx=10, pady=5)

        self.ai_vars['ai_text_enhancement_enabled'] = tk.BooleanVar(value=self.settings.get('ai_text_enhancement_enabled', False))
        text_check = ttk.Checkbutton(text_frame, text="Enable Qwen2.5-1.5B for LaTeX Enhancement",
                                   variable=self.ai_vars['ai_text_enhancement_enabled'])
        text_check.pack(anchor=tk.W)

        ttk.Label(text_frame, text="Uses AI to improve and perfect LaTeX output quality.\nSkips enhancement when disabled for faster processing.",
                 foreground="gray").pack(anchor=tk.W, pady=(5, 0))

        # Subject Processing AI Settings
        subject_frame = ttk.LabelFrame(scrollable_frame, text="Subject-Specific AI Processing", padding=10)
        subject_frame.pack(fill=tk.X, padx=10, pady=5)

        self.ai_vars['ai_subject_processing_enabled'] = tk.BooleanVar(value=self.settings.get('ai_subject_processing_enabled', False))
        subject_check = ttk.Checkbutton(subject_frame, text="Enable AI-Powered Subject Processing",
                                      variable=self.ai_vars['ai_subject_processing_enabled'])
        subject_check.pack(anchor=tk.W)

        ttk.Label(subject_frame, text="Uses AI for intelligent Mathematics, Chemistry, and Physics processing.\nUses basic rule-based processing when disabled.",
                 foreground="gray").pack(anchor=tk.W, pady=(5, 0))

        # Math Reasoning AI Settings
        reasoning_frame = ttk.LabelFrame(scrollable_frame, text="Mathematical Reasoning AI", padding=10)
        reasoning_frame.pack(fill=tk.X, padx=10, pady=5)

        self.ai_vars['ai_math_reasoning_enabled'] = tk.BooleanVar(value=self.settings.get('ai_math_reasoning_enabled', False))
        reasoning_check = ttk.Checkbutton(reasoning_frame, text="Enable AI Mathematical Reasoning",
                                        variable=self.ai_vars['ai_math_reasoning_enabled'])
        reasoning_check.pack(anchor=tk.W)

        ttk.Label(reasoning_frame, text="Uses AI to analyze and reason about mathematical expressions.\nSkips detailed analysis when disabled.",
                 foreground="gray").pack(anchor=tk.W, pady=(5, 0))

        # UI Settings
        ui_frame = ttk.LabelFrame(scrollable_frame, text="AI Status Display", padding=10)
        ui_frame.pack(fill=tk.X, padx=10, pady=5)

        self.ai_vars['ai_show_status'] = tk.BooleanVar(value=self.settings.get('ai_show_status', True))
        status_check = ttk.Checkbutton(ui_frame, text="Show AI Status Indicators in UI",
                                     variable=self.ai_vars['ai_show_status'])
        status_check.pack(anchor=tk.W)

        ttk.Label(ui_frame, text="Shows visual indicators when AI features are active or disabled.",
                 foreground="gray").pack(anchor=tk.W, pady=(5, 0))

        # Performance Info
        perf_frame = ttk.LabelFrame(scrollable_frame, text="Performance Information", padding=10)
        perf_frame.pack(fill=tk.X, padx=10, pady=5)

        perf_text = """Performance Benefits of Disabling AI:
• Faster response times (no AI model loading/processing)
• Lower memory usage (AI models not loaded)
• Reduced CPU/GPU usage
• More predictable processing times
• Still maintains high-quality LaTeX output using rule-based methods"""

        ttk.Label(perf_frame, text=perf_text, foreground="darkgreen").pack(anchor=tk.W)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Initial state update
        self._on_master_ai_toggle()

    def _on_master_ai_toggle(self):
        """Handle master AI toggle - enable/disable all AI options"""
        if hasattr(self, 'ai_vars'):
            master_enabled = self.ai_vars['ai_enabled'].get()

            # Enable/disable all AI sub-options based on master toggle
            for key, var in self.ai_vars.items():
                if key != 'ai_enabled' and key != 'ai_show_status':
                    if not master_enabled:
                        var.set(False)

    def _create_general_settings_tab(self, parent):
        """Create general settings tab"""
        # Export directory
        export_frame = ttk.LabelFrame(parent, text="Export Settings", padding=10)
        export_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(export_frame, text="Export Directory:").pack(anchor=tk.W)
        export_dir_frame = ttk.Frame(export_frame)
        export_dir_frame.pack(fill=tk.X, pady=(5, 0))

        self.export_dir_var = tk.StringVar(value=self.settings.get('export_dir', ''))
        ttk.Entry(export_dir_frame, textvariable=self.export_dir_var, width=50).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(export_dir_frame, text="Browse", command=self._browse_export_dir).pack(side=tk.RIGHT, padx=(5, 0))

        # Debug settings
        debug_frame = ttk.LabelFrame(parent, text="Debug Settings", padding=10)
        debug_frame.pack(fill=tk.X, padx=10, pady=5)

        self.debug_files_var = tk.BooleanVar(value=self.settings.get('debug_files', False))
        ttk.Checkbutton(debug_frame, text="Save debug files", variable=self.debug_files_var).pack(anchor=tk.W)

    def _create_export_settings_tab(self, parent):
        """Create export settings tab"""
        # Font settings
        font_frame = ttk.LabelFrame(parent, text="Font Settings", padding=10)
        font_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(font_frame, text="Font Family:").pack(anchor=tk.W)
        self.font_family_var = tk.StringVar(value=self.settings.get('font_family', 'Times New Roman'))
        font_combo = ttk.Combobox(font_frame, textvariable=self.font_family_var,
                                 values=['Times New Roman', 'Arial', 'Calibri', 'Computer Modern'])
        font_combo.pack(fill=tk.X, pady=(5, 10))

        ttk.Label(font_frame, text="Font Size:").pack(anchor=tk.W)
        self.font_size_var = tk.IntVar(value=self.settings.get('font_size', 12))
        ttk.Spinbox(font_frame, from_=8, to=24, textvariable=self.font_size_var).pack(anchor=tk.W, pady=(5, 0))

        # Page reference settings
        ref_frame = ttk.LabelFrame(parent, text="Reference Settings", padding=10)
        ref_frame.pack(fill=tk.X, padx=10, pady=5)

        self.show_page_refs_var = tk.BooleanVar(value=self.settings.get('show_page_refs', True))
        ttk.Checkbutton(ref_frame, text="Show page references in export", variable=self.show_page_refs_var).pack(anchor=tk.W)

        self.inline_equations_var = tk.BooleanVar(value=self.settings.get('inline_equations', False))
        ttk.Checkbutton(ref_frame, text="Use inline equations", variable=self.inline_equations_var).pack(anchor=tk.W)

    def _browse_export_dir(self):
        """Browse for export directory"""
        directory = filedialog.askdirectory(initialdir=self.export_dir_var.get())
        if directory:
            self.export_dir_var.set(directory)

    def _save_settings(self, window):
        """Save all settings and close dialog"""
        # Update AI settings
        if hasattr(self, 'ai_vars'):
            for key, var in self.ai_vars.items():
                self.settings[key] = var.get()

        # Update general settings
        if hasattr(self, 'export_dir_var'):
            self.settings['export_dir'] = self.export_dir_var.get()
        if hasattr(self, 'debug_files_var'):
            self.settings['debug_files'] = self.debug_files_var.get()
        if hasattr(self, 'font_family_var'):
            self.settings['font_family'] = self.font_family_var.get()
        if hasattr(self, 'font_size_var'):
            self.settings['font_size'] = self.font_size_var.get()
        if hasattr(self, 'show_page_refs_var'):
            self.settings['show_page_refs'] = self.show_page_refs_var.get()
        if hasattr(self, 'inline_equations_var'):
            self.settings['inline_equations'] = self.inline_equations_var.get()

        # Apply settings immediately
        self._apply_ai_settings()

        window.destroy()
        messagebox.showinfo("Settings", "Settings saved successfully!")

    def _reset_settings(self):
        """Reset all settings to defaults"""
        if messagebox.askyesno("Reset Settings", "Are you sure you want to reset all settings to defaults?"):
            # Reset AI settings to defaults (all disabled for performance)
            default_ai_settings = {
                'ai_enabled': False,
                'ai_vision_enabled': False,
                'ai_text_enhancement_enabled': False,
                'ai_subject_processing_enabled': False,
                'ai_math_reasoning_enabled': False,
                'ai_show_status': True,
            }

            # Update settings
            self.settings.update(default_ai_settings)

            # Update UI variables if they exist
            if hasattr(self, 'ai_vars'):
                for key, var in self.ai_vars.items():
                    if key in default_ai_settings:
                        var.set(default_ai_settings[key])
                self._on_master_ai_toggle()

    def _apply_ai_settings(self):
        """Apply AI settings immediately to current session"""
        print(f"🤖 AI Settings Applied:")
        print(f"   Master AI: {'Enabled' if self.settings.get('ai_enabled') else 'Disabled'}")
        print(f"   Vision AI: {'Enabled' if self.settings.get('ai_vision_enabled') else 'Disabled'}")
        print(f"   Text Enhancement: {'Enabled' if self.settings.get('ai_text_enhancement_enabled') else 'Disabled'}")
        print(f"   Subject Processing: {'Enabled' if self.settings.get('ai_subject_processing_enabled') else 'Disabled'}")
        print(f"   Math Reasoning: {'Enabled' if self.settings.get('ai_math_reasoning_enabled') else 'Disabled'}")

        # Update UI status indicators if needed
        if hasattr(self, 'ai_status_label'):
            self._update_ai_status_display()

    def _update_ai_status_display(self):
        """Update AI status indicators in the UI"""
        if not hasattr(self, 'ai_status_label'):
            return

        ai_enabled = self.settings.get('ai_enabled', False)
        show_status = self.settings.get('ai_show_status', True)

        if not show_status:
            self.ai_status_label.config(text="")
            return

        if ai_enabled:
            # Count enabled AI features
            enabled_features = []
            if self.settings.get('ai_vision_enabled', False):
                enabled_features.append("Vision")
            if self.settings.get('ai_text_enhancement_enabled', False):
                enabled_features.append("Enhancement")
            if self.settings.get('ai_subject_processing_enabled', False):
                enabled_features.append("Subject")
            if self.settings.get('ai_math_reasoning_enabled', False):
                enabled_features.append("Reasoning")

            if enabled_features:
                status_text = f"🤖 AI: {', '.join(enabled_features)}"
                color = "darkgreen"
            else:
                status_text = "🤖 AI: Enabled (No features)"
                color = "orange"
        else:
            status_text = "🚀 AI: Disabled (Fast Mode)"
            color = "blue"

        self.ai_status_label.config(text=status_text, foreground=color)

    def save_project(self):
        """Save current project session"""
        filename = filedialog.asksaveasfilename(
            title="Save Project",
            defaultextension=".lex",
            filetypes=[("LaTeX Extractor projects", "*.lex"), ("All files", "*.*")]
        )

        if filename:
            project = ProjectSession(
                files=self.current_files,
                equations=self.equation_queue,
                settings=self.settings
            )

            with open(filename, 'w') as f:
                json.dump(asdict(project), f, indent=2)

    def load_project(self):
        """Load project session"""
        filename = filedialog.askopenfilename(
            title="Load Project",
            filetypes=[("LaTeX Extractor projects", "*.lex"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r') as f:
                    data = json.load(f)

                # Restore project state
                self.current_files = data['files']
                self.settings.update(data['settings'])

                # Restore equations
                self.equation_queue = [
                    EquationRegion(**eq_data) for eq_data in data['equations']
                ]

                self.load_files()
                self.update_queue_display()

            except Exception as e:
                messagebox.showerror("Load Error", str(e))

    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = LaTeXExtractorByYark()
    app.run()