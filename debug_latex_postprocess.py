#!/usr/bin/env python3
"""
Debug the LaTeX post-processing to see why it's not working in the UI
"""

import re

def perfect_latex_postprocess(latex_text):
    """Post-process LaTeX-OCR output to make it 100% perfect"""
    print(f"🔍 DEBUG: Input LaTeX: '{latex_text}'")
    
    # Step 1: Ensure proper spacing around operators FIRST
    latex_text = re.sub(r'\s*=\s*', r' = ', latex_text)
    latex_text = re.sub(r'\s*>\s*', r' > ', latex_text)
    latex_text = re.sub(r'\s*<\s*', r' < ', latex_text)
    latex_text = re.sub(r'\s*\\leq\s*', r' \\leq ', latex_text)
    latex_text = re.sub(r'\s*\\geq\s*', r' \\geq ', latex_text)
    # Handle arrows specially - ensure they have spaces on both sides
    latex_text = re.sub(r'\s*\\Rightarrow\s*', r' \\Rightarrow ', latex_text)
    latex_text = re.sub(r'\s*\\Leftarrow\s*', r' \\Leftarrow ', latex_text)
    
    print(f"🔍 DEBUG: After operator spacing: '{latex_text}'")

    # Step 2: Fix missing multiplication symbols (be more careful to avoid duplicates)
    # Pattern: \mu(expression) -> \mu \cdot (expression) (but not if \cdot already exists)
    before_mu = latex_text
    latex_text = re.sub(r'(\\[a-zA-Z]+)(?!\s*\\cdot)\s*\(', r'\1 \\cdot (', latex_text)
    if before_mu != latex_text:
        print(f"🔍 DEBUG: After \\mu fix: '{latex_text}'")

    # Pattern: 50(expression) -> 50 \cdot (expression) (but not if \cdot already exists)
    before_num = latex_text
    latex_text = re.sub(r'(\d+)(?!\s*\\cdot)\s*\(', r'\1 \\cdot (', latex_text)
    if before_num != latex_text:
        print(f"🔍 DEBUG: After number fix: '{latex_text}'")

    # Pattern: \mu R -> \mu \cdot R (but not if \cdot already exists)
    latex_text = re.sub(r'(\\[a-zA-Z]+)(?!\s*\\cdot)\s*([A-Z])', r'\1 \\cdot \2', latex_text)

    # Pattern: 50\mu -> 50 \cdot \mu (but not if \cdot already exists)
    latex_text = re.sub(r'(\d+)(?!\s*\\cdot)\s*(\\[a-zA-Z]+)', r'\1 \\cdot \2', latex_text)

    # Step 3: Fix spacing around \cdot and clean up any double \cdot
    latex_text = re.sub(r'\s*\\cdot\s*', r' \\cdot ', latex_text)
    latex_text = re.sub(r'\\cdot\s+\\cdot', r'\\cdot', latex_text)  # Remove double \cdot

    # Step 4: Clean up extra spaces
    latex_text = re.sub(r'\s+', ' ', latex_text)
    latex_text = latex_text.strip()
    
    print(f"🔍 DEBUG: Final output: '{latex_text}'")
    
    return latex_text

def test_specific_cases():
    """Test the specific cases from the screenshot"""
    print("DEBUGGING LATEX POST-PROCESSING")
    print("=" * 50)
    
    # Test cases that should be fixed
    test_cases = [
        "\\Rightarrow 40 > \\mu(25\\sqrt{3}) + 50(\\frac{1}{2})",
        "\\mu(25\\sqrt{3})",
        "50(\\frac{1}{2})",
        "\\alpha(x)",
        "25(\\sqrt{3})",
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i} ---")
        result = perfect_latex_postprocess(test_input)
        
        # Check if multiplication symbols were added
        if "\\cdot" in result and "\\cdot" not in test_input:
            print("✅ SUCCESS: Multiplication symbols added!")
        elif "\\cdot" in test_input:
            print("ℹ️  Already had \\cdot symbols")
        else:
            print("❌ FAILED: No multiplication symbols added")
        
        print(f"Expected: Multiplication symbols between variables/numbers and parentheses")
        print(f"Got: {result}")

if __name__ == "__main__":
    test_specific_cases()
