#!/usr/bin/env python3
"""
Simple Chemistry Processing Test
"""

import re

def test_chemistry_processing():
    """Test chemistry processing with various inputs"""
    
    # Test cases that might come from OCR
    test_cases = [
        # What OCR might produce vs what we want
        "H 2 O + Na Cl -> Na + + Cl - + H 2 O",  # Spaced out
        "H2O + NaCl -> Na+ + Cl- + H2O",         # Proper format
        "2H2 + O2 -> 2H2O",                      # With coefficients
        "Ca(OH)2 + 2HCl -> CaCl2 + 2H2O",       # Complex formula
        "CH4 + 2O2 -> CO2 + 2H2O",              # Organic chemistry
        "Fe + CuSO4 -> FeSO4 + Cu",             # Metal displacement
        "H2SO4 + 2NaOH -> Na2SO4 + 2H2O",       # Acid-base
        "Mg + O2 -> MgO",                        # Simple synthesis
        "CaCO3 -> CaO + CO2",                    # Decomposition
        "AgNO3 + NaCl -> AgCl + NaNO3",         # Precipitation
    ]
    
    print("🧪 Chemistry Processing Test")
    print("=" * 50)
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: {test_input}")
        
        # Apply chemistry processing
        result = apply_chemistry_processing(test_input)
        print(f"🧪 Result: {result}")

def apply_chemistry_processing(text):
    """Apply comprehensive chemistry processing - IMPROVED VERSION"""

    # Step 1: Clean up spacing issues from OCR
    text = re.sub(r'\s+', ' ', text).strip()

    # Step 2: Fix spaced out chemical formulas (H 2 O -> H2O)
    text = re.sub(r'([A-Z][a-z]?)\s+(\d+)', r'\1\2', text)

    # Step 3: Fix spaced out charges (Na + -> Na+, Cl - -> Cl-)
    text = re.sub(r'([A-Z][a-z]?)\s*([+-]+)', r'\1\2', text)

    # Step 4: Fix spaced out compound names (Na Cl -> NaCl)
    text = re.sub(r'([A-Z][a-z]?)\s+([A-Z][a-z]?)(?=\s|$|\+|\-|→|->)', r'\1\2', text)

    # Step 5: Format reaction arrows FIRST
    text = re.sub(r'-->', r' \\rightarrow ', text)
    text = re.sub(r'->', r' \\rightarrow ', text)
    text = re.sub(r'<->', r' \\leftrightarrow ', text)
    text = re.sub(r'<-->', r' \\leftrightarrow ', text)

    # Step 6: Format chemical elements with \mathrm{} - IMPROVED ORDER
    elements = [
        'H', 'He', 'Li', 'Be', 'B', 'C', 'N', 'O', 'F', 'Ne',
        'Na', 'Mg', 'Al', 'Si', 'P', 'S', 'Cl', 'Ar', 'K', 'Ca',
        'Sc', 'Ti', 'V', 'Cr', 'Mn', 'Fe', 'Co', 'Ni', 'Cu', 'Zn',
        'Ga', 'Ge', 'As', 'Se', 'Br', 'Kr', 'Rb', 'Sr', 'Y', 'Zr',
        'Nb', 'Mo', 'Tc', 'Ru', 'Rh', 'Pd', 'Ag', 'Cd', 'In', 'Sn',
        'Sb', 'Te', 'I', 'Xe', 'Cs', 'Ba', 'La', 'Ce', 'Pr', 'Nd',
        'Pm', 'Sm', 'Eu', 'Gd', 'Tb', 'Dy', 'Ho', 'Er', 'Tm', 'Yb',
        'Lu', 'Hf', 'Ta', 'W', 'Re', 'Os', 'Ir', 'Pt', 'Au', 'Hg',
        'Tl', 'Pb', 'Bi', 'Po', 'At', 'Rn', 'Fr', 'Ra', 'Ac', 'Th',
        'Pa', 'U', 'Np', 'Pu', 'Am', 'Cm', 'Bk', 'Cf', 'Es', 'Fm',
        'Md', 'No', 'Lr', 'Rf', 'Db', 'Sg', 'Bh', 'Hs', 'Mt', 'Ds',
        'Rg', 'Cn', 'Nh', 'Fl', 'Mc', 'Lv', 'Ts', 'Og'
    ]

    # Format elements with subscripts FIRST (before standalone elements)
    for element in elements:
        # Element + number -> \mathrm{Element}_{number}
        pattern = rf'\b{re.escape(element)}(\d+)\b'
        replacement = rf'\\mathrm{{{element}}}_{{\1}}'
        text = re.sub(pattern, replacement, text)

    # Then format standalone elements
    for element in elements:
        # Standalone element -> \mathrm{Element} (avoid double wrapping)
        pattern = rf'(?<!\\mathrm\{{)\b{re.escape(element)}\b(?!\}}|_)'
        replacement = rf'\\mathrm{{{element}}}'
        text = re.sub(pattern, replacement, text)

    # Step 7: Format charges (IMPROVED - only at word boundaries)
    text = re.sub(r'(\\mathrm\{[^}]+\})([+-]+)(?=\s|$|\\s*\\rightarrow|\\s*\\leftrightarrow|\\s*\+)', r'\1^{\2}', text)

    # Step 8: Format parentheses in formulas
    text = re.sub(r'(\\mathrm\{[A-Z][a-z]?\})\(([^)]+)\)(\d+)', r'\1(\\mathrm{\2})_{\3}', text)

    # Step 9: Clean up spacing
    text = re.sub(r'\s*\+\s*', r' + ', text)
    text = re.sub(r'\s*\\rightarrow\s*', r' \\rightarrow ', text)
    text = re.sub(r'\s*\\leftrightarrow\s*', r' \\leftrightarrow ', text)
    text = re.sub(r'\s+', ' ', text).strip()

    return text

if __name__ == "__main__":
    test_chemistry_processing()
