#!/usr/bin/env python3
"""
Alternative Vision AI Processor for MathCapture Studio
Uses a more compatible vision model as fallback for Qwen-VL issues
"""

import os
import sys
import tempfile
import io
from PIL import Image
import time
import json

class AlternativeVisionProcessor:
    """Alternative vision AI processor using more compatible models"""
    
    def __init__(self):
        """Initialize alternative vision processor"""
        self.model_name = "microsoft/trocr-base-printed"  # More compatible OCR model
        self.available = False
        self.model_loaded = False
        self.model = None
        self.processor = None
        
        print("🔄 Initializing Alternative Vision AI for mathematical processing...")
        
        try:
            self._check_dependencies()
            self._initialize_model()
            
            if self.model_loaded:
                self.available = True
                print("✅ Alternative Vision AI ready for image-to-text conversion!")
            else:
                print("⚠️ Alternative Vision AI not loaded - will use LaTeX-OCR fallback")
                
        except Exception as e:
            print(f"⚠️ Alternative Vision AI initialization failed: {e}")
            print("🔄 Will use LaTeX-OCR processing as fallback")
            self.available = False

    def _check_dependencies(self):
        """Check if required dependencies are available"""
        try:
            # Check if transformers is available
            import transformers
            print("✅ Transformers library available")
            
            # Check if torch is available
            import torch
            print("✅ PyTorch available")
            
            # Check if we have GPU support
            if torch.cuda.is_available():
                print(f"✅ CUDA available - GPU: {torch.cuda.get_device_name(0)}")
            else:
                print("⚠️ CUDA not available - will use CPU")
                
            return True
            
        except ImportError as e:
            print(f"❌ Missing dependency: {e}")
            return False

    def _initialize_model(self):
        """Initialize the alternative vision model"""
        try:
            print("🔄 Loading alternative vision model...")
            
            from transformers import TrOCRProcessor, VisionEncoderDecoderModel
            import torch
            
            print(f"📥 Loading model from: {self.model_name}")
            
            # Load processor and model
            self.processor = TrOCRProcessor.from_pretrained(self.model_name)
            self.model = VisionEncoderDecoderModel.from_pretrained(self.model_name)
            
            # Move to appropriate device
            device = "cuda" if torch.cuda.is_available() else "cpu"
            self.model = self.model.to(device)
            self.device = device
            
            self.model_loaded = True
            print(f"✅ Alternative vision model loaded successfully on {device}")
            
        except Exception as e:
            print(f"❌ Failed to load alternative vision model: {e}")
            print("💡 This is a simpler fallback model")
            print("🔄 Application will use LaTeX-OCR as fallback")
            self.model_loaded = False

    def is_available(self):
        """Check if alternative vision AI is available and ready"""
        return self.available and self.model_loaded

    def process_image(self, image_input, subject="Mathematics"):
        """
        Process image with alternative vision AI
        
        Args:
            image_input: PIL Image object or path to the image file
            subject: Subject context (Mathematics, Chemistry, Physics)
            
        Returns:
            dict: {'latex': str, 'confidence': float}
        """
        if not self.is_available():
            print("❌ Alternative Vision AI not available")
            return None
            
        try:
            print(f"🔄 Processing image with Alternative Vision AI for {subject}...")
            
            # Handle both PIL Images and file paths
            image = self._prepare_image(image_input)
            if not image:
                print("❌ Failed to prepare image for processing")
                return None
            
            # Process image with TrOCR
            text_result = self._extract_text(image)
            
            if text_result:
                # Apply subject-specific post-processing
                latex_result = self._post_process_text(text_result, subject)
                confidence = 85.0  # Moderate confidence for alternative model
                
                result = {
                    'latex': latex_result,
                    'confidence': confidence
                }
                
                print(f"🔄✅ Alternative Vision AI processing successful: {latex_result}")
                return result
            else:
                print("❌ Alternative Vision AI processing failed")
                return None
                
        except Exception as e:
            print(f"❌ Alternative Vision AI processing error: {e}")
            return None

    def _prepare_image(self, image_input):
        """
        Prepare image for processing
        
        Args:
            image_input: PIL Image object or file path string
            
        Returns:
            PIL.Image: Prepared image
        """
        try:
            # If it's already a PIL Image, return it
            if isinstance(image_input, Image.Image):
                return image_input
            
            # If it's a file path, load it
            if isinstance(image_input, str) and os.path.exists(image_input):
                return Image.open(image_input)
            
            # If it's a numpy array, convert it
            if hasattr(image_input, 'shape'):  # numpy array
                if len(image_input.shape) == 3:
                    # Color image
                    return Image.fromarray(image_input)
                else:
                    # Grayscale image
                    return Image.fromarray(image_input, mode='L')
            
            print(f"❌ Unsupported image input type: {type(image_input)}")
            return None
            
        except Exception as e:
            print(f"❌ Error preparing image: {e}")
            return None

    def _extract_text(self, image):
        """Extract text from image using TrOCR"""
        try:
            import torch
            
            # Preprocess image
            pixel_values = self.processor(image, return_tensors="pt").pixel_values
            pixel_values = pixel_values.to(self.device)
            
            # Generate text
            with torch.no_grad():
                generated_ids = self.model.generate(pixel_values)
            
            # Decode text
            generated_text = self.processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
            
            return generated_text.strip()
            
        except Exception as e:
            print(f"❌ Text extraction error: {e}")
            return None

    def _post_process_text(self, text, subject):
        """Apply subject-specific post-processing to extracted text"""
        try:
            # Basic text to LaTeX conversion
            latex_text = text
            
            # Common mathematical symbols
            replacements = {
                'α': r'\alpha',
                'β': r'\beta',
                'γ': r'\gamma',
                'δ': r'\delta',
                'ε': r'\epsilon',
                'π': r'\pi',
                'σ': r'\sigma',
                'θ': r'\theta',
                'λ': r'\lambda',
                'μ': r'\mu',
                '∞': r'\infty',
                '∫': r'\int',
                '∑': r'\sum',
                '√': r'\sqrt',
                '≤': r'\leq',
                '≥': r'\geq',
                '≠': r'\neq',
                '±': r'\pm',
                '×': r'\times',
                '÷': r'\div',
                '²': r'^2',
                '³': r'^3',
            }
            
            for symbol, latex in replacements.items():
                latex_text = latex_text.replace(symbol, latex)
            
            # Subject-specific processing
            if subject == "Chemistry":
                # Basic chemistry formatting
                latex_text = self._format_chemistry(latex_text)
            elif subject == "Physics":
                # Basic physics formatting
                latex_text = self._format_physics(latex_text)
            else:  # Mathematics
                # Basic math formatting
                latex_text = self._format_mathematics(latex_text)
            
            return latex_text
            
        except Exception as e:
            print(f"❌ Post-processing error: {e}")
            return text  # Return original text if processing fails

    def _format_chemistry(self, text):
        """Apply basic chemistry formatting"""
        # Simple chemistry element detection and formatting
        import re
        
        # Format chemical elements (basic patterns)
        text = re.sub(r'\b([A-Z][a-z]?)(\d+)\b', r'\\mathrm{\1}_{\2}', text)
        text = text.replace('->', r' \\rightarrow ')
        text = text.replace('<->', r' \\leftrightarrow ')
        
        return text

    def _format_physics(self, text):
        """Apply basic physics formatting"""
        # Simple physics formatting
        text = text.replace('E=mc2', r'E = mc^2')
        text = text.replace('F=ma', r'F = ma')
        
        return text

    def _format_mathematics(self, text):
        """Apply basic mathematics formatting"""
        import re
        
        # Format fractions (basic pattern)
        text = re.sub(r'(\w+)/(\w+)', r'\\frac{\1}{\2}', text)
        
        # Format powers
        text = re.sub(r'(\w+)\^(\w+)', r'\1^{\2}', text)
        
        return text

    def get_model_info(self):
        """Get information about the loaded model"""
        if self.is_available():
            return {
                'name': 'Alternative Vision AI (TrOCR)',
                'model': self.model_name,
                'device': getattr(self, 'device', 'unknown'),
                'status': 'ready',
                'capabilities': ['vision', 'text_extraction', 'basic_math']
            }
        else:
            return {
                'name': 'Alternative Vision AI (TrOCR)',
                'model': self.model_name,
                'device': 'none',
                'status': 'not available',
                'capabilities': []
            }

if __name__ == "__main__":
    # Test the alternative vision processor
    processor = AlternativeVisionProcessor()
    
    if processor.is_available():
        print("✅ Alternative vision processor is ready!")
        print(f"📊 Model info: {processor.get_model_info()}")
    else:
        print("❌ Alternative vision processor is not available")
