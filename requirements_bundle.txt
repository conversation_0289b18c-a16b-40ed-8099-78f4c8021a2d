# LaTeX Extractor by Yark with Local LLM - Complete Bundle Requirements
# All dependencies needed for the bundled EXE application

# Core GUI and Image Processing
tkinter  # Usually included with Python
Pillow>=9.0.0
opencv-python>=4.5.0
numpy>=1.21.0

# PDF Processing
pdf2image>=3.1.0
poppler-utils  # System dependency for pdf2image

# Mathematical OCR
pix2tex>=0.1.0  # LaTeX-OCR

# HTTP Requests for Local LLM
requests>=2.28.0

# PyInstaller for bundling
PyInstaller>=5.0.0

# Optional: Better mathematical processing
sympy>=1.11.0  # For mathematical expression validation

# System dependencies (Windows)
# These are typically handled by PyInstaller automatically:
# - Visual C++ Redistributable
# - Windows API libraries

# Note: Ollama will be bundled separately as a binary
# The Local LLM model will be downloaded on first run
