#!/usr/bin/env python3
"""
AI-Powered Mathematical Content Processor
Uses AI reasoning to understand and convert mathematical expressions
instead of manual pattern matching for each case.
"""

import re
import json
from typing import Dict, List, Tuple, Optional

class AIMathProcessor:
    """
    AI-powered mathematical content processor that uses reasoning
    instead of hardcoded patterns to handle ANY mathematical expression.
    """

    def __init__(self):
        self.mathematical_context = {
            "symbols": {
                "greek": ["α", "β", "γ", "δ", "ε", "ζ", "η", "θ", "ι", "κ", "λ", "μ", "ν", "ξ", "ο", "π", "ρ", "σ", "τ", "υ", "φ", "χ", "ψ", "ω"],
                "operators": ["=", ">", "<", "≥", "≤", "≠", "≈", "∝", "∞", "∑", "∏", "∫", "∂", "∇"],
                "arrows": ["→", "←", "↑", "↓", "⇒", "⇐", "⇔", "↔"],
                "functions": ["sin", "cos", "tan", "log", "ln", "exp", "sqrt", "abs", "max", "min"]
            },
            "structures": {
                "fractions": r"(\d+|\w+)/(\d+|\w+)",
                "powers": r"(\w+)\^(\d+|\w+)",
                "roots": r"√(\w+|\d+)",
                "parentheses": r"\([^)]+\)",
                "brackets": r"\[[^\]]+\]"
            }
        }

    def process_mathematical_expression(self, raw_text: str) -> Dict[str, str]:
        """
        Main processing function that uses AI reasoning to understand
        and convert mathematical expressions to perfect LaTeX.
        """
        try:
            # Step 1: Analyze the mathematical content
            analysis = self.analyze_mathematical_content(raw_text)

            # Step 2: Apply intelligent reasoning
            reasoning = self.apply_mathematical_reasoning(analysis, raw_text)

            # Step 3: Generate perfect LaTeX
            latex_output = self.generate_perfect_latex(reasoning)

            # Step 4: Validate and refine
            final_latex = self.validate_and_refine(latex_output)

            return {
                "original": raw_text,
                "analysis": json.dumps(analysis, indent=2),
                "reasoning": reasoning["explanation"],
                "latex": final_latex,
                "confidence": reasoning["confidence"]
            }

        except Exception as e:
            return {
                "original": raw_text,
                "analysis": f"Error in analysis: {e}",
                "reasoning": "Failed to process",
                "latex": raw_text,  # Fallback to original
                "confidence": 0.0
            }

    def get_latex_only(self, raw_text: str) -> str:
        """
        Direct conversion to perfect LaTeX code - no reasoning, just clean output.
        Returns only LaTeX that you can copy and use anywhere.
        """
        try:
            # Direct LaTeX conversion without analysis/reasoning overhead
            latex_output = self.convert_to_perfect_latex(raw_text)
            return latex_output

        except Exception:
            # Fallback to original text if processing fails
            return raw_text

    def process_chemistry_expression(self, raw_text: str) -> Dict[str, str]:
        """
        AI-powered chemistry expression processing with domain-specific reasoning.
        """
        try:
            # Step 1: Analyze the chemistry content
            analysis = self.analyze_chemistry_content(raw_text)

            # Step 2: Apply chemistry-specific reasoning
            reasoning = self.apply_chemistry_reasoning(analysis, raw_text)

            # Step 3: Generate perfect chemistry LaTeX
            latex_output = self.generate_chemistry_latex(reasoning)

            # Step 4: Validate and refine chemistry output
            final_latex = self.validate_chemistry_latex(latex_output)

            return {
                "original": raw_text,
                "analysis": json.dumps(analysis, indent=2),
                "reasoning": reasoning["explanation"],
                "latex": final_latex,
                "confidence": reasoning["confidence"]
            }

        except Exception as e:
            return {
                "original": raw_text,
                "analysis": f"Error in chemistry analysis: {e}",
                "reasoning": "Failed to process chemistry expression",
                "latex": raw_text,  # Fallback to original
                "confidence": 0.0
            }

    def analyze_chemistry_content(self, text: str) -> Dict:
        """
        Analyze chemistry content using AI reasoning for chemical equations and formulas.
        """
        analysis = {
            "content_type": self.identify_chemistry_type(text),
            "chemical_elements": self.extract_chemical_elements(text),
            "reaction_components": self.analyze_reaction_structure(text),
            "context": self.determine_chemistry_context(text)
        }

        return analysis

    def identify_chemistry_type(self, text: str) -> str:
        """Intelligently identify the type of chemistry content."""
        # Chemical reaction indicators
        if any(arrow in text for arrow in ["→", "->", "⇌", "<->", "<=>"]):
            return "chemical_reaction"
        elif any(element in text for element in ["H2O", "CO2", "NaCl", "H2SO4"]):
            return "chemical_formula"
        elif any(symbol in text for symbol in ["+", "-", "2+", "3+"]):
            return "ionic_equation"
        elif "=" in text and any(element in text for element in ["H", "O", "C", "N"]):
            return "balanced_equation"
        else:
            return "general_chemistry"

    def extract_chemical_elements(self, text: str) -> list:
        """Extract chemical elements and compounds from text."""
        import re

        # Common chemical elements pattern
        element_pattern = r'\b([A-Z][a-z]?)(\d*)\b'
        elements = re.findall(element_pattern, text)

        return [{"element": elem[0], "subscript": elem[1]} for elem in elements if elem[0] in
                ["H", "He", "Li", "Be", "B", "C", "N", "O", "F", "Ne", "Na", "Mg", "Al", "Si", "P", "S", "Cl", "Ar", "K", "Ca"]]

    def analyze_reaction_structure(self, text: str) -> Dict:
        """Analyze the structure of chemical reactions."""
        structure = {
            "has_reactants": False,
            "has_products": False,
            "has_catalyst": False,
            "reaction_type": "unknown"
        }

        # Check for reaction arrows
        if any(arrow in text for arrow in ["→", "->", "⇌", "<->"]):
            structure["has_reactants"] = True
            structure["has_products"] = True

            if "⇌" in text or "<->" in text:
                structure["reaction_type"] = "equilibrium"
            else:
                structure["reaction_type"] = "forward"

        return structure

    def determine_chemistry_context(self, text: str) -> str:
        """Determine the chemistry context for better processing."""
        if any(term in text.lower() for term in ["acid", "base", "ph", "h+", "oh-"]):
            return "acid_base"
        elif any(term in text.lower() for term in ["oxidation", "reduction", "electron", "e-"]):
            return "redox"
        elif any(term in text.lower() for term in ["organic", "carbon", "hydrocarbon"]):
            return "organic"
        else:
            return "general"

    def apply_chemistry_reasoning(self, analysis: Dict, original_text: str = "") -> Dict:
        """
        Apply intelligent reasoning for chemistry expressions.
        """
        reasoning = {
            "explanation": "",
            "transformations": [],
            "confidence": 0.0,
            "original_text": original_text
        }

        content_type = analysis["content_type"]
        context = analysis["context"]

        # Chemistry-specific reasoning
        if content_type == "chemical_reaction":
            reasoning["explanation"] = "Detected chemical reaction. Will format with proper arrows and element notation."
            reasoning["transformations"].extend(["format_reaction_arrows", "format_chemical_elements"])
            reasoning["confidence"] = 0.95
        elif content_type == "chemical_formula":
            reasoning["explanation"] = "Detected chemical formula. Will ensure proper subscript formatting."
            reasoning["transformations"].extend(["format_subscripts", "format_elements"])
            reasoning["confidence"] = 0.9
        elif content_type == "ionic_equation":
            reasoning["explanation"] = "Detected ionic equation. Will format charges and ions properly."
            reasoning["transformations"].extend(["format_charges", "format_ions"])
            reasoning["confidence"] = 0.85
        else:
            reasoning["explanation"] = "General chemistry expression. Applying standard formatting."
            reasoning["transformations"].append("general_chemistry_format")
            reasoning["confidence"] = 0.7

        return reasoning

    def generate_chemistry_latex(self, reasoning: Dict) -> str:
        """
        Generate perfect LaTeX for chemistry expressions.
        """
        original_text = reasoning.get("original_text", "")
        transformations = reasoning.get("transformations", [])

        latex_output = original_text

        # Apply transformations based on reasoning
        for transformation in transformations:
            if transformation == "format_reaction_arrows":
                latex_output = self.format_reaction_arrows(latex_output)
            elif transformation == "format_chemical_elements":
                latex_output = self.format_chemical_elements(latex_output)
            elif transformation == "format_subscripts":
                latex_output = self.format_subscripts(latex_output)
            elif transformation == "format_charges":
                latex_output = self.format_charges(latex_output)
            elif transformation == "format_ions":
                latex_output = self.format_ions(latex_output)
            elif transformation == "general_chemistry_format":
                latex_output = self.general_chemistry_format(latex_output)

        return latex_output

    def validate_chemistry_latex(self, latex_text: str) -> str:
        """
        Validate and refine chemistry LaTeX output.
        """
        import re

        # Final cleanup and validation
        latex_text = re.sub(r'\s+', ' ', latex_text).strip()

        # Ensure proper spacing around arrows and operators
        latex_text = re.sub(r'\s*(\\rightarrow|\\leftarrow|\\leftrightarrow)\s*', r' \1 ', latex_text)
        latex_text = re.sub(r'\s*\+\s*', r' + ', latex_text)
        latex_text = re.sub(r'\s*=\s*', r' = ', latex_text)

        return latex_text

    def format_reaction_arrows(self, text: str) -> str:
        """Format chemical reaction arrows."""
        import re

        # Reaction arrow patterns
        arrow_patterns = [
            (r'<-->', r' \\leftrightarrow '),
            (r'<->', r' \\leftrightarrow '),
            (r'⇌', r' \\leftrightarrow '),
            (r'-->', r' \\rightarrow '),
            (r'->', r' \\rightarrow '),
            (r'→', r' \\rightarrow '),
        ]

        for pattern, replacement in arrow_patterns:
            text = re.sub(pattern, replacement, text)

        return text

    def format_chemical_elements(self, text: str) -> str:
        """Format chemical elements with proper notation."""
        import re

        # Element patterns
        text = re.sub(r'\b([A-Z][a-z]?)(\d+)\b', r'\\mathrm{\1}_{\2}', text)
        text = re.sub(r'\b([A-Z][a-z]?)\b', r'\\mathrm{\1}', text)

        return text

    def format_subscripts(self, text: str) -> str:
        """Format chemical subscripts."""
        import re

        text = re.sub(r'([A-Za-z])(\d+)', r'\1_{\2}', text)

        return text

    def format_charges(self, text: str) -> str:
        """Format ionic charges."""
        import re

        # Charge patterns
        text = re.sub(r'(\w+)(\d*)([+-]+)', r'\1^{\2\3}', text)

        return text

    def format_ions(self, text: str) -> str:
        """Format ionic species."""
        import re

        # Ion formatting
        text = re.sub(r'([A-Z][a-z]?)([+-])', r'\\mathrm{\1}^{\2}', text)

        return text

    def general_chemistry_format(self, text: str) -> str:
        """Apply general chemistry formatting."""
        text = self.format_chemical_elements(text)
        text = self.format_reaction_arrows(text)
        text = self.format_charges(text)

        return text

    def get_chemistry_latex_only(self, raw_text: str) -> str:
        """
        Get perfect chemistry LaTeX output only - no reasoning, no analysis

        Args:
            raw_text: Raw text from OCR

        Returns:
            str: Perfect chemistry LaTeX code only
        """
        try:
            # Direct LaTeX generation for chemistry
            latex_output = self.generate_chemistry_latex_direct(raw_text)

            # Validate and clean
            final_latex = self.validate_chemistry_latex(latex_output)

            return final_latex

        except Exception as e:
            # Fallback to basic formatting
            return self.basic_chemistry_format(raw_text)

    def generate_chemistry_latex_direct(self, text: str) -> str:
        """Generate chemistry LaTeX directly without reasoning overhead"""
        # Apply all chemistry formatting transformations
        latex_output = text

        # Format chemical elements
        latex_output = self.format_chemical_elements(latex_output)

        # Format reaction arrows
        latex_output = self.format_reaction_arrows(latex_output)

        # Format charges
        latex_output = self.format_charges(latex_output)

        # Format subscripts
        latex_output = self.format_subscripts(latex_output)

        # General chemistry formatting
        latex_output = self.general_chemistry_format(latex_output)

        return latex_output

    def basic_chemistry_format(self, text: str) -> str:
        """Basic chemistry formatting as fallback"""
        import re

        # Essential chemistry formatting
        text = re.sub(r'\b([A-Z][a-z]?)(\d+)\b', r'\\mathrm{\1}_{\2}', text)
        text = re.sub(r'-->', r' \\rightarrow ', text)
        text = re.sub(r'->', r' \\rightarrow ', text)
        text = re.sub(r'<->', r' \\leftrightarrow ', text)
        text = re.sub(r'(\w+)([+-]+)', r'\1^{\2}', text)

        return text.strip()

    def convert_to_perfect_latex(self, text: str) -> str:
        """
        Direct conversion to perfect LaTeX without reasoning overhead.
        Applies all necessary transformations for clean, usable LaTeX.
        """
        latex = text

        # Convert fractions
        latex = re.sub(r'(\d+)/(\d+)', r'\\frac{\1}{\2}', latex)
        latex = re.sub(r'([a-zA-Z]+)/([a-zA-Z]+)', r'\\frac{\1}{\2}', latex)
        latex = re.sub(r'\(([^)]+)/([^)]+)\)', r'\\left(\\frac{\1}{\2}\\right)', latex)

        # Convert powers and superscripts
        latex = re.sub(r'([a-zA-Z])\^(\d+)', r'\1^{\2}', latex)
        latex = re.sub(r'([a-zA-Z])\^([a-zA-Z]+)', r'\1^{\2}', latex)
        latex = re.sub(r'(\d+)°', r'\1^{\\circ}', latex)

        # Convert square roots
        latex = re.sub(r'√(\d+)', r'\\sqrt{\1}', latex)
        latex = re.sub(r'√\(([^)]+)\)', r'\\sqrt{\1}', latex)
        latex = re.sub(r'sqrt\(([^)]+)\)', r'\\sqrt{\1}', latex)

        # Convert operators with proper spacing
        latex = re.sub(r'\s*=\s*', ' = ', latex)
        latex = re.sub(r'\s*>\s*', ' > ', latex)
        latex = re.sub(r'\s*<\s*', ' < ', latex)
        latex = re.sub(r'\s*≥\s*', ' \\geq ', latex)
        latex = re.sub(r'\s*≤\s*', ' \\leq ', latex)
        latex = re.sub(r'\s*≠\s*', ' \\neq ', latex)
        latex = re.sub(r'\s*±\s*', ' \\pm ', latex)

        # Convert arrows
        latex = re.sub(r'\s*⇒\s*', ' \\Rightarrow ', latex)
        latex = re.sub(r'\s*=>\s*', ' \\Rightarrow ', latex)
        latex = re.sub(r'\s*→\s*', ' \\rightarrow ', latex)
        latex = re.sub(r'\s*←\s*', ' \\leftarrow ', latex)
        latex = re.sub(r'\s*↔\s*', ' \\leftrightarrow ', latex)

        # Convert functions
        latex = re.sub(r'\bsin\b', r'\\sin', latex)
        latex = re.sub(r'\bcos\b', r'\\cos', latex)
        latex = re.sub(r'\btan\b', r'\\tan', latex)
        latex = re.sub(r'\blog\b', r'\\log', latex)
        latex = re.sub(r'\bln\b', r'\\ln', latex)
        latex = re.sub(r'\bexp\b', r'\\exp', latex)
        latex = re.sub(r'\bmax\b', r'\\max', latex)
        latex = re.sub(r'\bmin\b', r'\\min', latex)

        # Convert Greek letters
        greek_map = {
            'α': '\\alpha', 'β': '\\beta', 'γ': '\\gamma', 'δ': '\\delta',
            'ε': '\\epsilon', 'θ': '\\theta', 'λ': '\\lambda', 'μ': '\\mu',
            'π': '\\pi', 'σ': '\\sigma', 'φ': '\\phi', 'ω': '\\omega'
        }
        for greek, latex_cmd in greek_map.items():
            latex = latex.replace(greek, latex_cmd)

        # Convert special symbols
        latex = latex.replace('∞', '\\infty')
        latex = latex.replace('∑', '\\sum')
        latex = latex.replace('∏', '\\prod')
        latex = latex.replace('∫', '\\int')
        latex = latex.replace('∂', '\\partial')
        latex = latex.replace('∇', '\\nabla')

        # Clean up spacing and syntax
        latex = re.sub(r'\s+', ' ', latex).strip()
        latex = latex.replace('{ }', '').replace('{}', '')

        return latex

    def analyze_mathematical_content(self, text: str) -> Dict:
        """
        Analyze mathematical content using AI reasoning instead of hardcoded patterns.
        """
        analysis = {
            "content_type": self.identify_content_type(text),
            "mathematical_elements": self.extract_mathematical_elements(text),
            "structure": self.analyze_structure(text),
            "context": self.determine_context(text)
        }

        return analysis

    def identify_content_type(self, text: str) -> str:
        """Intelligently identify the type of mathematical content."""
        # Use reasoning instead of hardcoded patterns
        if any(symbol in text for symbol in ["⇒", "→", "∴"]):
            return "logical_implication"
        elif any(op in text for op in [">", "<", "≥", "≤", "="]):
            return "equation_or_inequality"
        elif any(func in text for func in ["sin", "cos", "tan", "log"]):
            return "function_expression"
        elif any(symbol in text for symbol in ["∫", "∑", "∏"]):
            return "calculus_expression"
        elif "/" in text or "frac" in text:
            return "fraction_expression"
        else:
            return "general_mathematical"

    def extract_mathematical_elements(self, text: str) -> List[Dict]:
        """Extract and categorize mathematical elements intelligently."""
        elements = []

        # Numbers
        numbers = re.findall(r'\d+(?:\.\d+)?', text)
        for num in numbers:
            elements.append({"type": "number", "value": num, "latex": num})

        # Variables (single letters, Greek letters)
        variables = re.findall(r'[a-zA-Z]|[α-ωΑ-Ω]', text)
        for var in variables:
            if var in self.mathematical_context["symbols"]["greek"]:
                elements.append({"type": "greek_variable", "value": var, "latex": f"\\{self.greek_to_latex(var)}"})
            else:
                elements.append({"type": "variable", "value": var, "latex": var})

        # Operators
        for op in self.mathematical_context["symbols"]["operators"]:
            if op in text:
                elements.append({"type": "operator", "value": op, "latex": self.operator_to_latex(op)})

        # Functions
        for func in self.mathematical_context["symbols"]["functions"]:
            if func in text:
                elements.append({"type": "function", "value": func, "latex": f"\\{func}"})

        return elements

    def analyze_structure(self, text: str) -> Dict:
        """Analyze the structural organization of the mathematical expression."""
        structure = {
            "has_fractions": "/" in text or "frac" in text,
            "has_powers": "^" in text or any(c in "²³⁴⁵⁶⁷⁸⁹" for c in text),
            "has_roots": "√" in text or "sqrt" in text,
            "has_parentheses": "(" in text and ")" in text,
            "has_brackets": "[" in text and "]" in text,
            "has_implications": "⇒" in text or "→" in text,
            "complexity_level": self.assess_complexity(text)
        }

        return structure

    def determine_context(self, text: str) -> str:
        """Determine the mathematical context/domain."""
        if any(trig in text for trig in ["sin", "cos", "tan"]):
            return "trigonometry"
        elif any(calc in text for calc in ["∫", "d/dx", "lim"]):
            return "calculus"
        elif any(stat in text for stat in ["μ", "σ", "P(", "E["]):
            return "statistics"
        elif any(alg in text for alg in ["x", "y", "z", "="]):
            return "algebra"
        else:
            return "general"

    def apply_mathematical_reasoning(self, analysis: Dict, original_text: str = "") -> Dict:
        """
        Apply intelligent reasoning to understand the mathematical expression
        and determine the best LaTeX representation.
        """
        reasoning = {
            "explanation": "",
            "transformations": [],
            "confidence": 0.0,
            "original_text": original_text  # Store original text for processing
        }

        content_type = analysis["content_type"]
        structure = analysis["structure"]

        # Reasoning based on content type
        if content_type == "equation_or_inequality":
            reasoning["explanation"] = "Detected equation/inequality. Will ensure proper spacing around operators."
            reasoning["transformations"].append("normalize_operators")
            reasoning["confidence"] = 0.9

        elif content_type == "logical_implication":
            reasoning["explanation"] = "Detected logical implication. Will use proper arrow symbols."
            reasoning["transformations"].append("convert_arrows")
            reasoning["confidence"] = 0.95

        elif content_type == "function_expression":
            reasoning["explanation"] = "Detected function expression. Will format functions properly."
            reasoning["transformations"].append("format_functions")
            reasoning["confidence"] = 0.85

        # Additional reasoning based on structure
        if structure["has_fractions"]:
            reasoning["transformations"].append("format_fractions")
            reasoning["explanation"] += " Contains fractions - will use \\frac notation."

        if structure["has_powers"]:
            reasoning["transformations"].append("format_powers")
            reasoning["explanation"] += " Contains powers - will use proper superscript notation."

        return reasoning

    def generate_perfect_latex(self, reasoning: Dict) -> str:
        """
        Generate perfect LaTeX based on the reasoning analysis.
        This is the core AI-powered conversion function.
        """
        # Get the original text from the reasoning context
        original_text = reasoning.get("original_text", "")

        # Start with the original and apply intelligent transformations
        latex_output = original_text

        # Apply each transformation identified by reasoning
        for transformation in reasoning["transformations"]:
            if transformation == "normalize_operators":
                latex_output = self.normalize_operators_smart(latex_output)
            elif transformation == "convert_arrows":
                latex_output = self.convert_arrows_smart(latex_output)
            elif transformation == "format_functions":
                latex_output = self.format_functions_smart(latex_output)
            elif transformation == "format_fractions":
                latex_output = self.format_fractions_smart(latex_output)
            elif transformation == "format_powers":
                latex_output = self.format_powers_smart(latex_output)

        return latex_output

    def validate_and_refine(self, latex: str) -> str:
        """Final validation and refinement of LaTeX output."""
        # Remove extra spaces
        latex = re.sub(r'\s+', ' ', latex).strip()

        # Ensure proper LaTeX syntax
        latex = self.fix_latex_syntax(latex)

        return latex

    # Helper methods for smart transformations
    def normalize_operators_smart(self, text: str) -> str:
        """Intelligently normalize mathematical operators with proper spacing."""
        # Normalize equals signs
        text = re.sub(r'\s*=\s*', ' = ', text)

        # Normalize inequality operators
        text = re.sub(r'\s*>\s*', ' > ', text)
        text = re.sub(r'\s*<\s*', ' < ', text)
        text = re.sub(r'\s*≥\s*', ' \\geq ', text)
        text = re.sub(r'\s*≤\s*', ' \\leq ', text)
        text = re.sub(r'\s*≠\s*', ' \\neq ', text)

        return text

    def convert_arrows_smart(self, text: str) -> str:
        """Intelligently convert arrows and implications."""
        # Convert various arrow representations
        text = re.sub(r'\s*⇒\s*', ' \\Rightarrow ', text)
        text = re.sub(r'\s*=>\s*', ' \\Rightarrow ', text)
        text = re.sub(r'\s*→\s*', ' \\rightarrow ', text)
        text = re.sub(r'\s*←\s*', ' \\leftarrow ', text)

        # Handle LaTeX arrow commands with proper spacing
        text = re.sub(r'\s*\\Rightarrow\s*', ' \\Rightarrow ', text)
        text = re.sub(r'\s*\\rightarrow\s*', ' \\rightarrow ', text)

        return text

    def format_functions_smart(self, text: str) -> str:
        """Intelligently format mathematical functions."""
        # Trigonometric functions
        text = re.sub(r'\bsin\b', r'\\sin', text)
        text = re.sub(r'\bcos\b', r'\\cos', text)
        text = re.sub(r'\btan\b', r'\\tan', text)

        # Logarithmic functions
        text = re.sub(r'\blog\b', r'\\log', text)
        text = re.sub(r'\bln\b', r'\\ln', text)

        # Other functions
        text = re.sub(r'\bexp\b', r'\\exp', text)
        text = re.sub(r'\bmax\b', r'\\max', text)
        text = re.sub(r'\bmin\b', r'\\min', text)

        return text

    def format_fractions_smart(self, text: str) -> str:
        """Intelligently format fractions."""
        # Convert simple fractions like 1/2 to \frac{1}{2}
        text = re.sub(r'(\d+)/(\d+)', r'\\frac{\1}{\2}', text)

        # Convert more complex fractions
        text = re.sub(r'([a-zA-Z]+)/([a-zA-Z]+)', r'\\frac{\1}{\2}', text)

        # Handle parentheses around fractions
        text = re.sub(r'\(([^)]+)/([^)]+)\)', r'\\left(\\frac{\1}{\2}\\right)', text)

        return text

    def format_powers_smart(self, text: str) -> str:
        """Intelligently format powers and superscripts."""
        # Convert simple powers like x^2 to x^{2}
        text = re.sub(r'([a-zA-Z])\^(\d+)', r'\1^{\2}', text)

        # Convert degree symbols
        text = re.sub(r'(\d+)°', r'\1^{\\circ}', text)
        text = re.sub(r'(\d+)\^\\circ', r'\1^{\\circ}', text)

        # Handle more complex superscripts
        text = re.sub(r'([a-zA-Z])\^([a-zA-Z]+)', r'\1^{\2}', text)

        return text

    def fix_latex_syntax(self, latex: str) -> str:
        """Fix common LaTeX syntax issues."""
        # Remove double spaces
        latex = re.sub(r'\s+', ' ', latex)

        # Fix common issues
        latex = latex.replace('{ }', '')
        latex = latex.replace('{}', '')

        return latex.strip()

    # Utility methods
    def greek_to_latex(self, greek_char: str) -> str:
        """Convert Greek character to LaTeX command."""
        greek_map = {
            'α': 'alpha', 'β': 'beta', 'γ': 'gamma', 'δ': 'delta',
            'ε': 'epsilon', 'θ': 'theta', 'λ': 'lambda', 'μ': 'mu',
            'π': 'pi', 'σ': 'sigma', 'φ': 'phi', 'ω': 'omega'
        }
        return greek_map.get(greek_char, greek_char)

    def operator_to_latex(self, operator: str) -> str:
        """Convert operator to LaTeX."""
        op_map = {
            '≥': '\\geq', '≤': '\\leq', '≠': '\\neq',
            '⇒': '\\Rightarrow', '→': '\\rightarrow',
            '∞': '\\infty', '±': '\\pm'
        }
        return op_map.get(operator, operator)

    def assess_complexity(self, text: str) -> str:
        """Assess the complexity level of the mathematical expression."""
        complexity_score = 0

        if any(symbol in text for symbol in ["∫", "∑", "∏"]):
            complexity_score += 3
        if "/" in text or "frac" in text:
            complexity_score += 2
        if "^" in text:
            complexity_score += 1
        if any(func in text for func in ["sin", "cos", "tan", "log"]):
            complexity_score += 1

        if complexity_score >= 5:
            return "high"
        elif complexity_score >= 3:
            return "medium"
        else:
            return "low"
