#!/usr/bin/env python3
"""
AI-Powered Mathematical Content Processor
Uses AI reasoning to understand and convert mathematical expressions
instead of manual pattern matching for each case.
"""

import re
import json
from typing import Dict, List, Tuple, Optional

class AIMathProcessor:
    """
    AI-powered mathematical content processor that uses reasoning
    instead of hardcoded patterns to handle ANY mathematical expression.
    """

    def __init__(self):
        self.mathematical_context = {
            "symbols": {
                "greek": ["α", "β", "γ", "δ", "ε", "ζ", "η", "θ", "ι", "κ", "λ", "μ", "ν", "ξ", "ο", "π", "ρ", "σ", "τ", "υ", "φ", "χ", "ψ", "ω"],
                "operators": ["=", ">", "<", "≥", "≤", "≠", "≈", "∝", "∞", "∑", "∏", "∫", "∂", "∇"],
                "arrows": ["→", "←", "↑", "↓", "⇒", "⇐", "⇔", "↔"],
                "functions": ["sin", "cos", "tan", "log", "ln", "exp", "sqrt", "abs", "max", "min"]
            },
            "structures": {
                "fractions": r"(\d+|\w+)/(\d+|\w+)",
                "powers": r"(\w+)\^(\d+|\w+)",
                "roots": r"√(\w+|\d+)",
                "parentheses": r"\([^)]+\)",
                "brackets": r"\[[^\]]+\]"
            }
        }

    def process_mathematical_expression(self, raw_text: str) -> Dict[str, str]:
        """
        Main processing function that uses AI reasoning to understand
        and convert mathematical expressions to perfect LaTeX.
        """
        try:
            # Step 1: Analyze the mathematical content
            analysis = self.analyze_mathematical_content(raw_text)

            # Step 2: Apply intelligent reasoning
            reasoning = self.apply_mathematical_reasoning(analysis, raw_text)

            # Step 3: Generate perfect LaTeX
            latex_output = self.generate_perfect_latex(reasoning)

            # Step 4: Validate and refine
            final_latex = self.validate_and_refine(latex_output)

            return {
                "original": raw_text,
                "analysis": json.dumps(analysis, indent=2),
                "reasoning": reasoning["explanation"],
                "latex": final_latex,
                "confidence": reasoning["confidence"]
            }

        except Exception as e:
            return {
                "original": raw_text,
                "analysis": f"Error in analysis: {e}",
                "reasoning": "Failed to process",
                "latex": raw_text,  # Fallback to original
                "confidence": 0.0
            }

    def get_latex_only(self, raw_text: str) -> str:
        """
        Direct conversion to perfect LaTeX code - no reasoning, just clean output.
        Returns only LaTeX that you can copy and use anywhere.
        """
        try:
            # Direct LaTeX conversion without analysis/reasoning overhead
            latex_output = self.convert_to_perfect_latex(raw_text)
            return latex_output

        except Exception:
            # Fallback to original text if processing fails
            return raw_text

    def convert_to_perfect_latex(self, text: str) -> str:
        """
        Direct conversion to perfect LaTeX without reasoning overhead.
        Applies all necessary transformations for clean, usable LaTeX.
        """
        latex = text

        # Convert fractions
        latex = re.sub(r'(\d+)/(\d+)', r'\\frac{\1}{\2}', latex)
        latex = re.sub(r'([a-zA-Z]+)/([a-zA-Z]+)', r'\\frac{\1}{\2}', latex)
        latex = re.sub(r'\(([^)]+)/([^)]+)\)', r'\\left(\\frac{\1}{\2}\\right)', latex)

        # Convert powers and superscripts
        latex = re.sub(r'([a-zA-Z])\^(\d+)', r'\1^{\2}', latex)
        latex = re.sub(r'([a-zA-Z])\^([a-zA-Z]+)', r'\1^{\2}', latex)
        latex = re.sub(r'(\d+)°', r'\1^{\\circ}', latex)

        # Convert square roots
        latex = re.sub(r'√(\d+)', r'\\sqrt{\1}', latex)
        latex = re.sub(r'√\(([^)]+)\)', r'\\sqrt{\1}', latex)
        latex = re.sub(r'sqrt\(([^)]+)\)', r'\\sqrt{\1}', latex)

        # Convert operators with proper spacing
        latex = re.sub(r'\s*=\s*', ' = ', latex)
        latex = re.sub(r'\s*>\s*', ' > ', latex)
        latex = re.sub(r'\s*<\s*', ' < ', latex)
        latex = re.sub(r'\s*≥\s*', ' \\geq ', latex)
        latex = re.sub(r'\s*≤\s*', ' \\leq ', latex)
        latex = re.sub(r'\s*≠\s*', ' \\neq ', latex)
        latex = re.sub(r'\s*±\s*', ' \\pm ', latex)

        # Convert arrows
        latex = re.sub(r'\s*⇒\s*', ' \\Rightarrow ', latex)
        latex = re.sub(r'\s*=>\s*', ' \\Rightarrow ', latex)
        latex = re.sub(r'\s*→\s*', ' \\rightarrow ', latex)
        latex = re.sub(r'\s*←\s*', ' \\leftarrow ', latex)
        latex = re.sub(r'\s*↔\s*', ' \\leftrightarrow ', latex)

        # Convert functions
        latex = re.sub(r'\bsin\b', r'\\sin', latex)
        latex = re.sub(r'\bcos\b', r'\\cos', latex)
        latex = re.sub(r'\btan\b', r'\\tan', latex)
        latex = re.sub(r'\blog\b', r'\\log', latex)
        latex = re.sub(r'\bln\b', r'\\ln', latex)
        latex = re.sub(r'\bexp\b', r'\\exp', latex)
        latex = re.sub(r'\bmax\b', r'\\max', latex)
        latex = re.sub(r'\bmin\b', r'\\min', latex)

        # Convert Greek letters
        greek_map = {
            'α': '\\alpha', 'β': '\\beta', 'γ': '\\gamma', 'δ': '\\delta',
            'ε': '\\epsilon', 'θ': '\\theta', 'λ': '\\lambda', 'μ': '\\mu',
            'π': '\\pi', 'σ': '\\sigma', 'φ': '\\phi', 'ω': '\\omega'
        }
        for greek, latex_cmd in greek_map.items():
            latex = latex.replace(greek, latex_cmd)

        # Convert special symbols
        latex = latex.replace('∞', '\\infty')
        latex = latex.replace('∑', '\\sum')
        latex = latex.replace('∏', '\\prod')
        latex = latex.replace('∫', '\\int')
        latex = latex.replace('∂', '\\partial')
        latex = latex.replace('∇', '\\nabla')

        # Clean up spacing and syntax
        latex = re.sub(r'\s+', ' ', latex).strip()
        latex = latex.replace('{ }', '').replace('{}', '')

        return latex

    def analyze_mathematical_content(self, text: str) -> Dict:
        """
        Analyze mathematical content using AI reasoning instead of hardcoded patterns.
        """
        analysis = {
            "content_type": self.identify_content_type(text),
            "mathematical_elements": self.extract_mathematical_elements(text),
            "structure": self.analyze_structure(text),
            "context": self.determine_context(text)
        }

        return analysis

    def identify_content_type(self, text: str) -> str:
        """Intelligently identify the type of mathematical content."""
        # Use reasoning instead of hardcoded patterns
        if any(symbol in text for symbol in ["⇒", "→", "∴"]):
            return "logical_implication"
        elif any(op in text for op in [">", "<", "≥", "≤", "="]):
            return "equation_or_inequality"
        elif any(func in text for func in ["sin", "cos", "tan", "log"]):
            return "function_expression"
        elif any(symbol in text for symbol in ["∫", "∑", "∏"]):
            return "calculus_expression"
        elif "/" in text or "frac" in text:
            return "fraction_expression"
        else:
            return "general_mathematical"

    def extract_mathematical_elements(self, text: str) -> List[Dict]:
        """Extract and categorize mathematical elements intelligently."""
        elements = []

        # Numbers
        numbers = re.findall(r'\d+(?:\.\d+)?', text)
        for num in numbers:
            elements.append({"type": "number", "value": num, "latex": num})

        # Variables (single letters, Greek letters)
        variables = re.findall(r'[a-zA-Z]|[α-ωΑ-Ω]', text)
        for var in variables:
            if var in self.mathematical_context["symbols"]["greek"]:
                elements.append({"type": "greek_variable", "value": var, "latex": f"\\{self.greek_to_latex(var)}"})
            else:
                elements.append({"type": "variable", "value": var, "latex": var})

        # Operators
        for op in self.mathematical_context["symbols"]["operators"]:
            if op in text:
                elements.append({"type": "operator", "value": op, "latex": self.operator_to_latex(op)})

        # Functions
        for func in self.mathematical_context["symbols"]["functions"]:
            if func in text:
                elements.append({"type": "function", "value": func, "latex": f"\\{func}"})

        return elements

    def analyze_structure(self, text: str) -> Dict:
        """Analyze the structural organization of the mathematical expression."""
        structure = {
            "has_fractions": "/" in text or "frac" in text,
            "has_powers": "^" in text or any(c in "²³⁴⁵⁶⁷⁸⁹" for c in text),
            "has_roots": "√" in text or "sqrt" in text,
            "has_parentheses": "(" in text and ")" in text,
            "has_brackets": "[" in text and "]" in text,
            "has_implications": "⇒" in text or "→" in text,
            "complexity_level": self.assess_complexity(text)
        }

        return structure

    def determine_context(self, text: str) -> str:
        """Determine the mathematical context/domain."""
        if any(trig in text for trig in ["sin", "cos", "tan"]):
            return "trigonometry"
        elif any(calc in text for calc in ["∫", "d/dx", "lim"]):
            return "calculus"
        elif any(stat in text for stat in ["μ", "σ", "P(", "E["]):
            return "statistics"
        elif any(alg in text for alg in ["x", "y", "z", "="]):
            return "algebra"
        else:
            return "general"

    def apply_mathematical_reasoning(self, analysis: Dict, original_text: str = "") -> Dict:
        """
        Apply intelligent reasoning to understand the mathematical expression
        and determine the best LaTeX representation.
        """
        reasoning = {
            "explanation": "",
            "transformations": [],
            "confidence": 0.0,
            "original_text": original_text  # Store original text for processing
        }

        content_type = analysis["content_type"]
        structure = analysis["structure"]

        # Reasoning based on content type
        if content_type == "equation_or_inequality":
            reasoning["explanation"] = "Detected equation/inequality. Will ensure proper spacing around operators."
            reasoning["transformations"].append("normalize_operators")
            reasoning["confidence"] = 0.9

        elif content_type == "logical_implication":
            reasoning["explanation"] = "Detected logical implication. Will use proper arrow symbols."
            reasoning["transformations"].append("convert_arrows")
            reasoning["confidence"] = 0.95

        elif content_type == "function_expression":
            reasoning["explanation"] = "Detected function expression. Will format functions properly."
            reasoning["transformations"].append("format_functions")
            reasoning["confidence"] = 0.85

        # Additional reasoning based on structure
        if structure["has_fractions"]:
            reasoning["transformations"].append("format_fractions")
            reasoning["explanation"] += " Contains fractions - will use \\frac notation."

        if structure["has_powers"]:
            reasoning["transformations"].append("format_powers")
            reasoning["explanation"] += " Contains powers - will use proper superscript notation."

        return reasoning

    def generate_perfect_latex(self, reasoning: Dict) -> str:
        """
        Generate perfect LaTeX based on the reasoning analysis.
        This is the core AI-powered conversion function.
        """
        # Get the original text from the reasoning context
        original_text = reasoning.get("original_text", "")

        # Start with the original and apply intelligent transformations
        latex_output = original_text

        # Apply each transformation identified by reasoning
        for transformation in reasoning["transformations"]:
            if transformation == "normalize_operators":
                latex_output = self.normalize_operators_smart(latex_output)
            elif transformation == "convert_arrows":
                latex_output = self.convert_arrows_smart(latex_output)
            elif transformation == "format_functions":
                latex_output = self.format_functions_smart(latex_output)
            elif transformation == "format_fractions":
                latex_output = self.format_fractions_smart(latex_output)
            elif transformation == "format_powers":
                latex_output = self.format_powers_smart(latex_output)

        return latex_output

    def validate_and_refine(self, latex: str) -> str:
        """Final validation and refinement of LaTeX output."""
        # Remove extra spaces
        latex = re.sub(r'\s+', ' ', latex).strip()

        # Ensure proper LaTeX syntax
        latex = self.fix_latex_syntax(latex)

        return latex

    # Helper methods for smart transformations
    def normalize_operators_smart(self, text: str) -> str:
        """Intelligently normalize mathematical operators with proper spacing."""
        # Normalize equals signs
        text = re.sub(r'\s*=\s*', ' = ', text)

        # Normalize inequality operators
        text = re.sub(r'\s*>\s*', ' > ', text)
        text = re.sub(r'\s*<\s*', ' < ', text)
        text = re.sub(r'\s*≥\s*', ' \\geq ', text)
        text = re.sub(r'\s*≤\s*', ' \\leq ', text)
        text = re.sub(r'\s*≠\s*', ' \\neq ', text)

        return text

    def convert_arrows_smart(self, text: str) -> str:
        """Intelligently convert arrows and implications."""
        # Convert various arrow representations
        text = re.sub(r'\s*⇒\s*', ' \\Rightarrow ', text)
        text = re.sub(r'\s*=>\s*', ' \\Rightarrow ', text)
        text = re.sub(r'\s*→\s*', ' \\rightarrow ', text)
        text = re.sub(r'\s*←\s*', ' \\leftarrow ', text)

        # Handle LaTeX arrow commands with proper spacing
        text = re.sub(r'\s*\\Rightarrow\s*', ' \\Rightarrow ', text)
        text = re.sub(r'\s*\\rightarrow\s*', ' \\rightarrow ', text)

        return text

    def format_functions_smart(self, text: str) -> str:
        """Intelligently format mathematical functions."""
        # Trigonometric functions
        text = re.sub(r'\bsin\b', r'\\sin', text)
        text = re.sub(r'\bcos\b', r'\\cos', text)
        text = re.sub(r'\btan\b', r'\\tan', text)

        # Logarithmic functions
        text = re.sub(r'\blog\b', r'\\log', text)
        text = re.sub(r'\bln\b', r'\\ln', text)

        # Other functions
        text = re.sub(r'\bexp\b', r'\\exp', text)
        text = re.sub(r'\bmax\b', r'\\max', text)
        text = re.sub(r'\bmin\b', r'\\min', text)

        return text

    def format_fractions_smart(self, text: str) -> str:
        """Intelligently format fractions."""
        # Convert simple fractions like 1/2 to \frac{1}{2}
        text = re.sub(r'(\d+)/(\d+)', r'\\frac{\1}{\2}', text)

        # Convert more complex fractions
        text = re.sub(r'([a-zA-Z]+)/([a-zA-Z]+)', r'\\frac{\1}{\2}', text)

        # Handle parentheses around fractions
        text = re.sub(r'\(([^)]+)/([^)]+)\)', r'\\left(\\frac{\1}{\2}\\right)', text)

        return text

    def format_powers_smart(self, text: str) -> str:
        """Intelligently format powers and superscripts."""
        # Convert simple powers like x^2 to x^{2}
        text = re.sub(r'([a-zA-Z])\^(\d+)', r'\1^{\2}', text)

        # Convert degree symbols
        text = re.sub(r'(\d+)°', r'\1^{\\circ}', text)
        text = re.sub(r'(\d+)\^\\circ', r'\1^{\\circ}', text)

        # Handle more complex superscripts
        text = re.sub(r'([a-zA-Z])\^([a-zA-Z]+)', r'\1^{\2}', text)

        return text

    def fix_latex_syntax(self, latex: str) -> str:
        """Fix common LaTeX syntax issues."""
        # Remove double spaces
        latex = re.sub(r'\s+', ' ', latex)

        # Fix common issues
        latex = latex.replace('{ }', '')
        latex = latex.replace('{}', '')

        return latex.strip()

    # Utility methods
    def greek_to_latex(self, greek_char: str) -> str:
        """Convert Greek character to LaTeX command."""
        greek_map = {
            'α': 'alpha', 'β': 'beta', 'γ': 'gamma', 'δ': 'delta',
            'ε': 'epsilon', 'θ': 'theta', 'λ': 'lambda', 'μ': 'mu',
            'π': 'pi', 'σ': 'sigma', 'φ': 'phi', 'ω': 'omega'
        }
        return greek_map.get(greek_char, greek_char)

    def operator_to_latex(self, operator: str) -> str:
        """Convert operator to LaTeX."""
        op_map = {
            '≥': '\\geq', '≤': '\\leq', '≠': '\\neq',
            '⇒': '\\Rightarrow', '→': '\\rightarrow',
            '∞': '\\infty', '±': '\\pm'
        }
        return op_map.get(operator, operator)

    def assess_complexity(self, text: str) -> str:
        """Assess the complexity level of the mathematical expression."""
        complexity_score = 0

        if any(symbol in text for symbol in ["∫", "∑", "∏"]):
            complexity_score += 3
        if "/" in text or "frac" in text:
            complexity_score += 2
        if "^" in text:
            complexity_score += 1
        if any(func in text for func in ["sin", "cos", "tan", "log"]):
            complexity_score += 1

        if complexity_score >= 5:
            return "high"
        elif complexity_score >= 3:
            return "medium"
        else:
            return "low"
