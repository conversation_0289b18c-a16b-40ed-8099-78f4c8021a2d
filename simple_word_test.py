#!/usr/bin/env python3
"""
Simple test for Word compatibility fixes
"""

import re

def fix_word_compatibility(latex_text):
    """Fix LaTeX to be compatible with Microsoft Word equation editor"""

    if not latex_text or not latex_text.strip():
        return ""

    print(f"🔧 Word compatibility input: {latex_text}")

    # 1. Remove ALL array environments (Word doesn't support them)
    # Handle the specific pattern from your screenshot first
    latex_text = re.sub(r'\\begin\{array\}\{[^}]*\}([^&]*?)&\{([^}]*?)\}\\end\{array\}', r'\1 \2', latex_text)

    # Handle simpler array patterns
    latex_text = re.sub(r'\\begin\{array\}\{[^}]*\}(.*?)\\end\{array\}', r'\1', latex_text, flags=re.DOTALL)
    latex_text = re.sub(r'\\begin\{array\}(.*?)\\end\{array\}', r'\1', latex_text, flags=re.DOTALL)

    # 2. Remove other unsupported environments
    unsupported_envs = ['aligned', 'align', 'gather', 'multline', 'split']
    for env in unsupported_envs:
        latex_text = re.sub(rf'\\begin\{{{env}\*?\}}(.*?)\\end\{{{env}\*?\}}', r'\1', latex_text, flags=re.DOTALL)

    # 3. Fix column specifications and alignment characters
    latex_text = re.sub(r'\{[rlc]+\}', '', latex_text)  # Remove column specs like {rl}
    latex_text = re.sub(r'&', ' ', latex_text)  # Remove alignment characters

    # 4. Remove unsupported sizing commands
    latex_text = re.sub(r'\\[Bb]igg?[lr]?', '', latex_text)

    # 5. Simplify complex structures that Word can't handle
    latex_text = re.sub(r'\\left\\{', '{', latex_text)
    latex_text = re.sub(r'\\right\\}', '}', latex_text)

    # 6. Clean up multiple spaces and formatting
    latex_text = re.sub(r'\s+', ' ', latex_text)
    latex_text = latex_text.strip()

    # 7. Remove any remaining braces around simple expressions
    latex_text = re.sub(r'^\{(.*)\}$', r'\1', latex_text)

    # 8. Add proper spacing around operators for Word
    latex_text = re.sub(r'\s*>\s*', r' > ', latex_text)
    latex_text = re.sub(r'\s*<\s*', r' < ', latex_text)
    latex_text = re.sub(r'\s*=\s*', r' = ', latex_text)
    latex_text = re.sub(r'\s*\\Rightarrow\s*', r' \\Rightarrow ', latex_text)

    # 9. Add multiplication symbols where needed for clarity
    latex_text = re.sub(r'(\d+)\(', r'\1 \\cdot (', latex_text)
    latex_text = re.sub(r'\\mu\(', r'\\mu \\cdot (', latex_text)

    # 10. Final cleanup
    latex_text = re.sub(r'\s+', ' ', latex_text).strip()

    print(f"🔧 Word compatibility output: {latex_text}")

    return latex_text

def test_fix():
    """Test the Word compatibility fix"""
    
    # Test the problematic LaTeX from the screenshot
    problematic_latex = r"\begin{array}{r}\Rightarrow&{140>\mu(25(\sqrt{3}))+50\left(\frac{1}{2}\right)}\end{array}"
    
    print("🔧 Testing Word Compatibility Fix")
    print("=" * 50)
    print(f"Original LaTeX: {problematic_latex}")
    print()
    
    # Apply the Word compatibility fix
    fixed_latex = fix_word_compatibility(problematic_latex)
    
    print(f"Fixed LaTeX: {fixed_latex}")
    print()
    
    # Verify the fix
    if "\\begin{array}" not in fixed_latex:
        print("✅ SUCCESS: Array environments removed")
    else:
        print("❌ FAILED: Array environments still present")
    
    if "&" not in fixed_latex:
        print("✅ SUCCESS: Alignment characters removed")
    else:
        print("❌ FAILED: Alignment characters still present")
    
    print()
    print("📋 Copy this LaTeX to test in Word:")
    print(f"{fixed_latex}")
    print()
    print("🎯 This should work in Microsoft Word equation editor!")

if __name__ == "__main__":
    test_fix()
