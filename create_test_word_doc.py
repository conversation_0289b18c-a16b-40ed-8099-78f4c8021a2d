#!/usr/bin/env python3
"""
Create a test Word document with mathematical equations to demonstrate the improved functionality
"""

import sys
import os
sys.path.append('.')

from main import MathCaptureStudio, EquationRegion
from docx import Document

def create_test_document():
    """Create a test Word document with sample equations"""
    print("Creating test Word document with mathematical equations...")
    
    app = MathCaptureStudio()
    
    # Create sample equations
    sample_equations = [
        EquationRegion(
            x=100, y=100, width=200, height=50,
            page_num=1, filename="Test Document",
            latex_text="\\frac{a}{b} = \\frac{c}{d}",
            confidence=95.0
        ),
        EquationRegion(
            x=100, y=200, width=250, height=60,
            page_num=1, filename="Test Document", 
            latex_text="x^{2} + y^{2} = r^{2}",
            confidence=92.0
        ),
        EquationRegion(
            x=100, y=300, width=300, height=70,
            page_num=1, filename="Test Document",
            latex_text="\\sqrt{a^{2} + b^{2}} = c",
            confidence=88.0
        ),
        EquationRegion(
            x=100, y=400, width=350, height=80,
            page_num=1, filename="Test Document",
            latex_text="\\alpha + \\beta = \\gamma",
            confidence=90.0
        ),
        EquationRegion(
            x=100, y=500, width=400, height=90,
            page_num=1, filename="Test Document",
            latex_text="\\int_{0}^{\\infty} e^{-x} dx = 1",
            confidence=85.0
        )
    ]
    
    # Add equations to the app's queue
    app.equation_queue = sample_equations
    
    # Create the Word document
    filename = "test_mathematical_equations.docx"
    try:
        app.create_word_document(filename)
        print(f"✅ Successfully created: {filename}")
        print(f"📄 Document contains {len(sample_equations)} mathematical equations")
        print("\nEquations included:")
        for i, eq in enumerate(sample_equations, 1):
            print(f"  {i}. {eq.latex_text} (Confidence: {eq.confidence}%)")
        
        print(f"\n📁 File saved in: {os.path.abspath(filename)}")
        print("\n🎯 Open the document in Microsoft Word to see the properly formatted equations!")
        
    except Exception as e:
        print(f"❌ Error creating document: {e}")

if __name__ == "__main__":
    create_test_document()
