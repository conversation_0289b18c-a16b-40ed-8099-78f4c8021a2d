#!/usr/bin/env python3
"""
Bundle Script for MathCapture Studio with Local LLM
Creates a complete EXE package with embedded Ollama + Llama model
No external installations required by end users.
"""

import os
import sys
import shutil
import subprocess
import urllib.request
import zipfile
from pathlib import Path

class LLMBundler:
    """
    Bundles MathCapture Studio with Local LLM capabilities
    """
    
    def __init__(self):
        self.project_dir = Path(__file__).parent
        self.bundle_dir = self.project_dir / "bundle"
        self.ollama_dir = self.bundle_dir / "ollama"
        self.models_dir = self.bundle_dir / "models"
        
        # Ollama download URLs
        self.ollama_urls = {
            "windows": "https://github.com/ollama/ollama/releases/latest/download/ollama-windows-amd64.zip",
            "linux": "https://github.com/ollama/ollama/releases/latest/download/ollama-linux-amd64.tgz",
            "mac": "https://github.com/ollama/ollama/releases/latest/download/ollama-darwin.zip"
        }
        
        # Model to bundle (lightweight for math)
        self.model_name = "llama3.2:1b"
    
    def create_bundle_structure(self):
        """Create the bundle directory structure"""
        print("📁 Creating bundle structure...")
        
        # Clean and create directories
        if self.bundle_dir.exists():
            shutil.rmtree(self.bundle_dir)
        
        self.bundle_dir.mkdir()
        self.ollama_dir.mkdir()
        self.models_dir.mkdir()
        
        print(f"✅ Bundle structure created at: {self.bundle_dir}")
    
    def download_ollama(self):
        """Download Ollama for the target platform"""
        print("📥 Downloading Ollama...")
        
        # Detect platform
        platform = "windows" if os.name == 'nt' else "linux"
        url = self.ollama_urls[platform]
        
        # Download
        download_path = self.bundle_dir / f"ollama-{platform}.zip"
        print(f"Downloading from: {url}")
        
        try:
            urllib.request.urlretrieve(url, download_path)
            print(f"✅ Ollama downloaded to: {download_path}")
            
            # Extract
            self.extract_ollama(download_path, platform)
            
        except Exception as e:
            print(f"❌ Failed to download Ollama: {e}")
            return False
        
        return True
    
    def extract_ollama(self, download_path, platform):
        """Extract Ollama to bundle directory"""
        print("📦 Extracting Ollama...")
        
        try:
            if platform == "windows":
                with zipfile.ZipFile(download_path, 'r') as zip_ref:
                    zip_ref.extractall(self.ollama_dir)
            else:
                # For Linux/Mac, use tar
                import tarfile
                with tarfile.open(download_path, 'r:gz') as tar_ref:
                    tar_ref.extractall(self.ollama_dir)
            
            print(f"✅ Ollama extracted to: {self.ollama_dir}")
            
            # Clean up download
            download_path.unlink()
            
        except Exception as e:
            print(f"❌ Failed to extract Ollama: {e}")
    
    def download_model(self):
        """Download the math model"""
        print(f"📥 Downloading model: {self.model_name}")
        
        # This would require Ollama to be running
        # For now, we'll create a script to download on first run
        self.create_model_download_script()
    
    def create_model_download_script(self):
        """Create a script to download the model on first run"""
        script_content = f'''#!/usr/bin/env python3
"""
Model Download Script
Downloads the math model on first application run
"""

import subprocess
import os
from pathlib import Path

def download_model():
    """Download the math model"""
    try:
        ollama_path = Path(__file__).parent / "ollama" / "ollama.exe"
        if not ollama_path.exists():
            ollama_path = Path(__file__).parent / "ollama" / "ollama"
        
        if ollama_path.exists():
            print("📥 Downloading math model (first run only)...")
            result = subprocess.run([str(ollama_path), "pull", "{self.model_name}"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Model downloaded successfully!")
                return True
            else:
                print(f"❌ Model download failed: {{result.stderr}}")
                return False
        else:
            print("❌ Ollama executable not found")
            return False
            
    except Exception as e:
        print(f"❌ Model download error: {{e}}")
        return False

if __name__ == "__main__":
    download_model()
'''
        
        script_path = self.bundle_dir / "download_model.py"
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        print(f"✅ Model download script created: {script_path}")
    
    def copy_application_files(self):
        """Copy the main application files to bundle"""
        print("📋 Copying application files...")
        
        files_to_copy = [
            "main.py",
            "local_llm_processor.py",
            "ai_math_processor.py",
            "latex_ocr_processor.py",
            "requirements.txt"
        ]
        
        for file_name in files_to_copy:
            src = self.project_dir / file_name
            dst = self.bundle_dir / file_name
            
            if src.exists():
                shutil.copy2(src, dst)
                print(f"✅ Copied: {file_name}")
            else:
                print(f"⚠️ File not found: {file_name}")
    
    def create_launcher_script(self):
        """Create a launcher script that initializes everything"""
        launcher_content = '''#!/usr/bin/env python3
"""
MathCapture Studio Launcher with Local LLM
Initializes Ollama and starts the application
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def start_ollama():
    """Start Ollama server"""
    try:
        ollama_path = Path(__file__).parent / "ollama" / "ollama.exe"
        if not ollama_path.exists():
            ollama_path = Path(__file__).parent / "ollama" / "ollama"
        
        if ollama_path.exists():
            print("🚀 Starting Ollama server...")
            process = subprocess.Popen([str(ollama_path), "serve"], 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE)
            
            # Wait a moment for server to start
            time.sleep(3)
            return process
        else:
            print("⚠️ Ollama not found - running without Local LLM")
            return None
            
    except Exception as e:
        print(f"⚠️ Failed to start Ollama: {e}")
        return None

def main():
    """Main launcher function"""
    print("🎯 LaTeX Extractor by Yark with Local LLM")
    print("=" * 50)

    # Start Ollama
    ollama_process = start_ollama()

    try:
        # Start main application
        print("🚀 Starting LaTeX Extractor by Yark...")
        from main import main as app_main
        app_main()
        
    except KeyboardInterrupt:
        print("\\n🛑 Application interrupted by user")
    except Exception as e:
        print(f"❌ Application error: {e}")
    finally:
        # Clean up Ollama process
        if ollama_process:
            try:
                ollama_process.terminate()
                ollama_process.wait(timeout=5)
                print("✅ Ollama server stopped")
            except:
                ollama_process.kill()
                print("🔄 Ollama server force-killed")

if __name__ == "__main__":
    main()
'''
        
        launcher_path = self.bundle_dir / "launch.py"
        with open(launcher_path, 'w') as f:
            f.write(launcher_content)
        
        print(f"✅ Launcher script created: {launcher_path}")
    
    def create_pyinstaller_spec(self):
        """Create PyInstaller spec file for bundling"""
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['launch.py'],
    pathex=['{self.bundle_dir}'],
    binaries=[
        ('ollama/*', 'ollama/'),
    ],
    datas=[
        ('*.py', '.'),
        ('download_model.py', '.'),
    ],
    hiddenimports=[
        'PIL',
        'cv2',
        'numpy',
        'tkinter',
        'requests',
        'pdf2image',
        'pix2tex',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='LaTeXExtractorByYark',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
        
        spec_path = self.bundle_dir / "LaTeXExtractorByYark.spec"
        with open(spec_path, 'w') as f:
            f.write(spec_content)
        
        print(f"✅ PyInstaller spec created: {spec_path}")
    
    def build_executable(self):
        """Build the final executable"""
        print("🔨 Building executable with PyInstaller...")
        
        try:
            # Change to bundle directory
            os.chdir(self.bundle_dir)
            
            # Run PyInstaller
            result = subprocess.run([
                sys.executable, "-m", "PyInstaller",
                "--onefile",
                "--name", "LaTeXExtractorByYark",
                "LaTeXExtractorByYark.spec"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Executable built successfully!")
                print(f"📦 Output: {self.bundle_dir / 'dist' / 'LaTeXExtractorByYark.exe'}")
            else:
                print(f"❌ Build failed: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Build error: {e}")
    
    def bundle_complete_application(self):
        """Complete bundling process"""
        print("🚀 Starting MathCapture Studio + Local LLM bundling...")
        print("=" * 60)
        
        # Step 1: Create structure
        self.create_bundle_structure()
        
        # Step 2: Download Ollama
        if not self.download_ollama():
            print("❌ Bundling failed - could not download Ollama")
            return False
        
        # Step 3: Setup model download
        self.download_model()
        
        # Step 4: Copy application files
        self.copy_application_files()
        
        # Step 5: Create launcher
        self.create_launcher_script()
        
        # Step 6: Create PyInstaller spec
        self.create_pyinstaller_spec()
        
        # Step 7: Build executable
        self.build_executable()
        
        print("=" * 60)
        print("🎉 Bundling complete!")
        print(f"📦 Your complete application is in: {self.bundle_dir / 'dist'}")
        print("✅ End users can run the EXE without any installations!")
        
        return True

def main():
    """Main bundling function"""
    bundler = LLMBundler()
    bundler.bundle_complete_application()

if __name__ == "__main__":
    main()
