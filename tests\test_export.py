"""
Tests for Word document export functionality in MathCapture Studio
"""

import unittest
import os
import tempfile
from docx import Document

# Import the word exporter from components
from components.word_exporter import WordExporter

class TestWordExport(unittest.TestCase):
    
    def setUp(self):
        """Set up test environment"""
        self.exporter = WordExporter()
        self.test_equations = [
            {
                'latex': '\\int x^2 dx',
                'confidence': 95.5,
                'page_num': 1,
                'filename': 'test.pdf'
            },
            {
                'latex': '\\sum_{i=1}^n i',
                'confidence': 92.3,
                'page_num': 2,
                'filename': 'test.pdf'
            }
        ]
    
    def test_document_creation(self):
        """Test basic document creation"""
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as tmp:
            output_path = tmp.name
        
        try:
            # Export test equations
            self.exporter.export_to_word(
                equations=self.test_equations,
                output_path=output_path,
                title="Test Document"
            )
            
            # Verify file was created
            self.assertTrue(os.path.exists(output_path))
            
            # Open and verify document
            doc = Document(output_path)
            self.assertEqual(len(doc.paragraphs), 4)  # Title + 2 equations + summary
            
        finally:
            if os.path.exists(output_path):
                os.remove(output_path)
    
    def test_equation_formatting(self):
        """Test equation formatting in document"""
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as tmp:
            output_path = tmp.name
        
        try:
            self.exporter.export_to_word(
                equations=self.test_equations,
                output_path=output_path
            )
            
            doc = Document(output_path)
            # Check first equation was included
            self.assertIn('∫ x^2 dx', doc.paragraphs[1].text)
            
        finally:
            if os.path.exists(output_path):
                os.remove(output_path)
    
if __name__ == '__main__':
    unittest.main()